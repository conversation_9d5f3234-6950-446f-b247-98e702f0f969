pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven {
            url = uri("https://repo.seres.cn/nexus/repository/maven-JM3.0-SNAPSHOT/")
            credentials {
                username = "jmdev"
                password = "CZPJ0Iz3Gi8j"
            }
        }
        maven {
            url = uri("https://repo.seres.cn/nexus/repository/maven-JM3.0/")
            credentials {
                username = "jmdev"
                password = "CZPJ0Iz3Gi8j"
            }
        }
        maven {
            url = uri("https://repo.seres.cn/nexus/repository/maven/")
            credentials {
                username = "jmdev"
                password = "CZPJ0Iz3Gi8j"
            }
        }
        google()
        mavenCentral()
    }
}

rootProject.name = "OTAAndroid"
include ':UsbUpgradeService'