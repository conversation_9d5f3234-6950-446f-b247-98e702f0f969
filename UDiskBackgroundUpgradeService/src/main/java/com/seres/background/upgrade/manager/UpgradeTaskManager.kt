package com.seres.background.upgrade.manager

import android.content.Context
import com.google.gson.Gson
import com.seres.background.upgrade.model.*
import com.seres.background.upgrade.monitor.StatusMonitor
import com.seres.background.upgrade.publisher.S2STaskPublisher
import com.seres.background.upgrade.utils.LogUtils
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.FileWriter
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors

/**
 * 升级任务管理器
 * 负责处理升级任务的文件拷贝、JSON生成和S2S通信
 */
class UpgradeTaskManager private constructor() {

    private val TAG = "UpgradeTaskManager"
    private val gson = Gson()
    private val threadPool = Executors.newCachedThreadPool()
    private val activeTasks = ConcurrentHashMap<String, UpgradeTaskManagerInfo>()

    private var s2sTaskPublisher: S2STaskPublisher? = null
    private var context: Context? = null
    private var statusMonitor: StatusMonitor? = null
    
    companion object {
        @Volatile
        private var INSTANCE: UpgradeTaskManager? = null

        fun getInstance(): UpgradeTaskManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UpgradeTaskManager().also { INSTANCE = it }
            }
        }

        fun getInstance(context: Context): UpgradeTaskManager {
            return getInstance().apply {
                if (this.context == null) {
                    this.context = context.applicationContext
                    this.statusMonitor = StatusMonitor(context)
                }
            }
        }

        // 目标共享目录
        const val SHARE_PATH = "/ota_share/"
    }
    
    /**
     * 升级任务信息
     */
    data class UpgradeTaskInfo(
        val taskId: String,
        val upgradeTask: UpgradeTask,
        val usbPath: String,
        val status: TaskStatus,
        val createTime: Long = System.currentTimeMillis()
    )
    

    /**
     * 设置S2S任务发布者
     */
    fun setS2STaskPublisher(publisher: S2STaskPublisher) {
        this.s2sTaskPublisher = publisher
    }
    
    /**
     * 处理升级任务信息（新方法）
     */
    fun processUpgradeTaskInfo(upgradeTaskInfo: com.seres.background.upgrade.model.UpgradeTaskInfo, usbDir: File, validPackages: List<DcItem>) {
        val taskId = generateTaskId()
        val taskInfo = UpgradeTaskManagerInfo(
            taskId = taskId,
            upgradeTaskInfo = upgradeTaskInfo,
            usbPath = usbDir.absolutePath,
            status = TaskStatus.PENDING,
            validPackages = validPackages
        )

        activeTasks[taskId] = taskInfo
        LogUtils.i(TAG, "开始处理升级任务信息: $taskId，包含 ${validPackages.size} 个升级包")
        LogUtils.d(TAG, "包ID: ${upgradeTaskInfo.dpPackage.pkgId}")
        LogUtils.d(TAG, "版本: ${upgradeTaskInfo.dpPackage.pkgInfo.pkgVer}")

        // 检查StatusMonitor是否可用
        if (statusMonitor == null) {
            LogUtils.w(TAG, "StatusMonitor为null，尝试重新初始化")
            context?.let { ctx ->
                statusMonitor = StatusMonitor(ctx)
                LogUtils.i(TAG, "StatusMonitor重新初始化成功")
            }
        }

        // 异步处理任务
        threadPool.execute {
            try {
                updateTaskStatus(taskId, TaskStatus.COPYING)

                // 1. 拷贝升级文件到目标目录
                val success = copyUpgradeFilesFromTaskInfo(usbDir, validPackages)

                // 2. 生成并保存任务JSON（无论拷贝是否成功都要保存）
                val taskJson = gson.newBuilder().setPrettyPrinting().create().toJson(upgradeTaskInfo)
                saveTaskJsonToStatus(taskId, taskJson)

                if (success) {
                    // 3. 更新任务状态为准备就绪
                    updateTaskStatus(taskId, TaskStatus.READY)

                    // 4. 通过S2S发布任务
                    publishTaskToS2S(taskId, taskJson)

                    LogUtils.i(TAG, "升级任务信息处理成功: $taskId")
                } else {
                    updateTaskStatus(taskId, TaskStatus.FAILED)
                    LogUtils.e(TAG, "升级任务文件拷贝失败，但JSON已保存: $taskId")
                }

            } catch (e: Exception) {
                updateTaskStatus(taskId, TaskStatus.FAILED)
                LogUtils.e(TAG, "处理升级任务失败: $taskId - ${e.message}", e)
            }
        }
    }

    /**
     * 处理升级任务（动态生成task_json）
     */
    // fun processUpgradeTask(upgradeTask: UpgradeTask, usbDir: File) {
    //     val taskId = generateTaskId()
    //     val taskInfo = UpgradeTaskManagerInfo(
    //         taskId = taskId,
    //         upgradeTask = upgradeTask,
    //         usbPath = usbDir.absolutePath,
    //         status = TaskStatus.PENDING
    //     )

    //     activeTasks[taskId] = taskInfo
    //     LogUtils.i(TAG, "开始处理升级任务: $taskId，包含 ${upgradeTask.packageInfo.size} 个升级包")
    //     LogUtils.d(TAG, "任务详情: ${gson.toJson(upgradeTask)}")

    //     // 检查StatusMonitor是否可用
    //     if (statusMonitor == null) {
    //         LogUtils.w(TAG, "StatusMonitor为null，尝试重新初始化")
    //         context?.let { ctx ->
    //             statusMonitor = StatusMonitor(ctx)
    //             LogUtils.i(TAG, "StatusMonitor重新初始化成功")
    //         }
    //     }
        
    //     threadPool.submit {
    //         try {
    //             // 1. 更新任务状态为拷贝中
    //             updateTaskStatus(taskId, TaskStatus.COPYING)
                
    //             // 2. 拷贝文件到目标目录
    //             val success = copyUpgradeFiles(usbDir, upgradeTask)
                
    //             // 3. 生成升级任务JSON（无论拷贝是否成功都要生成）
    //             val taskJson = generateUpgradeTaskJson(upgradeTask)

    //             // 4. 保存任务JSON到status文件夹（无论拷贝是否成功都要保存）
    //             saveTaskJsonToStatus(taskId, taskJson)

    //             if (success) {
    //                 // 5. 更新任务状态为准备就绪
    //                 updateTaskStatus(taskId, TaskStatus.READY)

    //                 // 6. 通过S2S发布任务
    //                 publishTaskToS2S(taskId, taskJson)

    //                 LogUtils.i(TAG, "升级任务处理成功: $taskId")
    //             } else {
    //                 updateTaskStatus(taskId, TaskStatus.FAILED)
    //                 LogUtils.e(TAG, "升级任务文件拷贝失败，但JSON已保存: $taskId")
    //             }
                
    //         } catch (e: Exception) {
    //             LogUtils.e(TAG, "处理升级任务失败 $taskId: ${e.message}")
    //             updateTaskStatus(taskId, TaskStatus.FAILED)
    //         }
    //     }
    // }
    
    /**
     * 从任务信息拷贝升级文件到目标目录
     */
    private fun copyUpgradeFilesFromTaskInfo(usbDir: File, validPackages: List<DcItem>): Boolean {
        return try {
            LogUtils.i(TAG, "开始拷贝升级文件到: $SHARE_PATH")

            // 尝试多个可能的目标目录
            val possibleDirs = listOf(
                SHARE_PATH,
                "/sdcard/ota_share/",
                "/storage/emulated/0/ota_share/",
                context?.getExternalFilesDir("ota_share")?.absolutePath ?: ""
            ).filter { it.isNotEmpty() }

            var shareDir: File? = null

            // 找到一个可用的目录
            for (dirPath in possibleDirs) {
                val testDir = File(dirPath)
                try {
                    if (!testDir.exists()) {
                        testDir.mkdirs()
                    }

                    // 测试写权限
                    val testFile = File(testDir, "test_write_permission.tmp")
                    testFile.writeText("test")
                    testFile.delete()

                    shareDir = testDir
                    LogUtils.i(TAG, "使用目标目录: ${testDir.absolutePath}")
                    break
                } catch (e: Exception) {
                    LogUtils.w(TAG, "目录 $dirPath 不可用: ${e.message}")
                }
            }

            if (shareDir == null) {
                LogUtils.e(TAG, "无法找到可用的目标目录")
                return false
            }

            // 拷贝所有升级包文件
            var successCount = 0
            for (dcItem in validPackages) {
                val sourceFileName = extractFileNameFromPath(dcItem.path)
                val sourceFile = findFileInUsbDir(usbDir, sourceFileName)

                if (sourceFile != null && sourceFile.exists()) {
                    try {
                        val targetFile = File(shareDir, sourceFileName)
                        copyFile(sourceFile, targetFile)
                        LogUtils.d(TAG, "文件拷贝成功: ${sourceFile.name} -> ${targetFile.absolutePath}")
                        successCount++
                    } catch (e: Exception) {
                        LogUtils.e(TAG, "拷贝文件失败 ${sourceFile.name}: ${e.message}")
                    }
                } else {
                    LogUtils.e(TAG, "源文件不存在: $sourceFileName")
                }
            }

            val totalFiles = validPackages.size
            LogUtils.i(TAG, "文件拷贝完成: $successCount/$totalFiles 成功")

            // 只要有文件拷贝成功就认为成功
            successCount > 0

        } catch (e: Exception) {
            LogUtils.e(TAG, "拷贝升级文件失败: ${e.message}", e)
            false
        }
    }
    
    /**
     * 在目录中查找文件
     */
    private fun findFileInDirectory(dir: File, fileName: String): File? {
        dir.listFiles()?.forEach { file ->
            if (file.isFile && file.name == fileName) {
                return file
            } else if (file.isDirectory) {
                val found = findFileInDirectory(file, fileName)
                if (found != null) return found
            }
        }
        return null
    }
    
    /**
     * 拷贝文件
     */
    private fun copyFile(source: File, target: File) {
        FileInputStream(source).use { input ->
            FileOutputStream(target).use { output ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    output.write(buffer, 0, bytesRead)
                }
                output.flush()
            }
        }
    }
    
    /**
     * 生成升级任务JSON
     */
    private fun generateUpgradeTaskJson(upgradeTask: UpgradeTask): String {
        return gson.newBuilder().setPrettyPrinting().create().toJson(upgradeTask)
    }

    /**
     * 保存任务JSON到status文件夹
     */
    private fun saveTaskJsonToStatus(taskId: String, taskJson: String) {
        try {
            LogUtils.d(TAG, "开始保存任务JSON: $taskId")

            if (statusMonitor == null) {
                LogUtils.e(TAG, "StatusMonitor为null，无法保存任务JSON")
                return
            }

            val statusPaths = statusMonitor!!.getStatusFilePaths()
            val statusDir = statusPaths["statusDir"]

            if (statusDir == null) {
                LogUtils.e(TAG, "状态目录路径为null")
                return
            }

            LogUtils.d(TAG, "状态目录: $statusDir")

            // 确保目录存在
            val statusDirFile = File(statusDir)
            if (!statusDirFile.exists()) {
                statusDirFile.mkdirs()
                LogUtils.i(TAG, "创建状态目录: $statusDir")
            }

            // 保存带任务ID的JSON文件
            val taskJsonFile = File(statusDir, "upgrade_task_${taskId}.json")
            FileWriter(taskJsonFile).use { writer ->
                writer.write(taskJson)
                writer.flush()
            }
            LogUtils.i(TAG, "升级任务JSON已保存: ${taskJsonFile.absolutePath}")

            // 同时保存最新的任务JSON（覆盖式）
            val latestTaskJsonFile = File(statusDir, "latest_upgrade_task.json")
            FileWriter(latestTaskJsonFile).use { writer ->
                writer.write(taskJson)
                writer.flush()
            }
            LogUtils.i(TAG, "最新升级任务JSON已保存: ${latestTaskJsonFile.absolutePath}")

        } catch (e: Exception) {
            LogUtils.e(TAG, "保存任务JSON到status文件夹失败: ${e.message}", e)
        }
    }
    
    /**
     * 通过S2S发布任务
     */
    private fun publishTaskToS2S(taskId: String, taskJson: String) {
        s2sTaskPublisher?.publishUpgradeTask(taskId, taskJson) { success ->
            if (success) {
                updateTaskStatus(taskId, TaskStatus.PUBLISHED)
                LogUtils.i(TAG, "升级任务发布成功: $taskId")
            } else {
                updateTaskStatus(taskId, TaskStatus.FAILED)
                LogUtils.e(TAG, "升级任务发布失败: $taskId")
            }
        }
    }
    
    /**
     * 更新任务状态
     */
    private fun updateTaskStatus(taskId: String, status: TaskStatus) {
        activeTasks[taskId]?.let { taskInfo: UpgradeTaskManagerInfo ->
            val updatedTaskInfo = taskInfo.copy(status = status)
            activeTasks[taskId] = updatedTaskInfo
            LogUtils.d(TAG, "任务状态更新: $taskId -> $status")
        }
    }
    
    /**
     * 根据USB路径取消任务
     */
    fun cancelTasksByUsbPath(usbPath: String) {
        LogUtils.i(TAG, "取消USB路径相关的任务: $usbPath")
        
        val tasksToCancel = activeTasks.values.filter { taskInfo: UpgradeTaskManagerInfo ->
            taskInfo.usbPath == usbPath
        }

        for (taskInfo in tasksToCancel) {
            updateTaskStatus(taskInfo.taskId, TaskStatus.CANCELLED)
            activeTasks.remove(taskInfo.taskId)
            LogUtils.i(TAG, "已取消任务: ${taskInfo.taskId}")
        }
    }
    
    /**
     * 生成任务ID
     */
    private fun generateTaskId(): String {
        return "UPGRADE_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    /**
     * 获取活跃任务列表
     */
    fun getActiveTasks(): List<UpgradeTaskManagerInfo> {
        return activeTasks.values.toList()
    }

    /**
     * 获取指定任务
     */
    fun getTask(taskId: String): UpgradeTaskManagerInfo? {
        return activeTasks[taskId]
    }

    /**
     * 从路径中提取文件名
     */
    private fun extractFileNameFromPath(path: String): String {
        return if (path.startsWith("http")) {
            // 如果是URL，提取最后的文件名部分
            path.substringAfterLast("/")
        } else {
            // 如果是本地路径，直接提取文件名
            File(path).name
        }
    }

    /**
     * 在USB目录中查找文件
     */
    private fun findFileInUsbDir(usbDir: File, fileName: String): File? {
        return try {
            val allFiles = getAllFilesRecursive(usbDir)
            allFiles.find { file ->
                file.name.equals(fileName, ignoreCase = true) ||
                file.name.contains(fileName, ignoreCase = true)
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "查找文件失败: $fileName - ${e.message}")
            null
        }
    }

    /**
     * 递归获取目录下所有文件
     */
    private fun getAllFilesRecursive(dir: File): List<File> {
        val files = mutableListOf<File>()

        try {
            dir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    files.add(file)
                } else if (file.isDirectory) {
                    files.addAll(getAllFilesRecursive(file))
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "扫描目录失败: ${dir.absolutePath} - ${e.message}")
        }

        return files
    }
}
