package com.seres.background.upgrade.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.IBinder
import androidx.core.app.NotificationCompat
import android.R
import com.seres.background.upgrade.analyzer.UpgradeTaskAnalyzer
import com.seres.background.upgrade.monitor.StatusMonitor
import com.seres.background.upgrade.utils.LogUtils
import java.io.File
import java.util.concurrent.Executors

/**
 * USB检测服务
 * 监听USB设备插拔，检测upgrade_task_info.json文件
 */
class UsbDetectionService : Service() {
    
    private val TAG = "UsbDetectionService"
    private val threadPool = Executors.newCachedThreadPool()
    private var upgradeTaskAnalyzer: UpgradeTaskAnalyzer? = null
    private var statusMonitor: StatusMonitor? = null
    
    companion object {
        const val NOTIFICATION_ID = 2001
        const val CHANNEL_ID = "usb_detection_channel"
        
        // Action常量
        const val ACTION_USB_ATTACHED = "com.seres.background.upgrade.USB_ATTACHED"
        const val ACTION_USB_DETACHED = "com.seres.background.upgrade.USB_DETACHED"
        const val ACTION_MEDIA_MOUNTED = "com.seres.background.upgrade.MEDIA_MOUNTED"
        const val ACTION_MEDIA_UNMOUNTED = "com.seres.background.upgrade.MEDIA_UNMOUNTED"
        
        // Extra常量
        const val EXTRA_DEVICE_PATH = "device_path"
        
        // 配置文件名
        const val UPGRADE_TASK_FILE = "upgrade_task_info.json"
    }
    
    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "UsbDetectionService onCreate")
        
        // 创建前台服务通知
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        upgradeTaskAnalyzer = UpgradeTaskAnalyzer()
        statusMonitor = StatusMonitor(this)

        // 设置分析器的状态监控器和上下文
        upgradeTaskAnalyzer?.setStatusMonitor(statusMonitor!!)
        upgradeTaskAnalyzer?.setContext(this)

        // 检查已挂载的USB设备
        checkExistingUsbDevices()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.i(TAG, "UsbDetectionService onStartCommand: ${intent?.action} - flags: $flags, startId: $startId")

        // 确保前台服务正常运行
        try {
            startForeground(NOTIFICATION_ID, createNotification())
            LogUtils.i(TAG, "USB检测服务前台通知已更新")
        } catch (e: Exception) {
            LogUtils.e(TAG, "更新USB检测服务前台通知失败: ${e.message}")
        }

        // 重新初始化组件（防止重启后组件丢失）
        if (upgradeTaskAnalyzer == null || statusMonitor == null) {
            LogUtils.i(TAG, "检测到USB服务组件丢失，重新初始化")
            upgradeTaskAnalyzer = UpgradeTaskAnalyzer()
            statusMonitor = StatusMonitor(this)
            upgradeTaskAnalyzer?.setStatusMonitor(statusMonitor!!)
            upgradeTaskAnalyzer?.setContext(this)
        }

        intent?.let { handleIntent(it) }

        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        LogUtils.i(TAG, "UsbDetectionService onDestroy")
        threadPool.shutdown()
    }
    
    /**
     * 处理Intent
     */
    private fun handleIntent(intent: Intent) {
        val action = intent.action ?: return
        val devicePath = intent.getStringExtra(EXTRA_DEVICE_PATH) ?: return
        
        when (action) {
            ACTION_USB_ATTACHED,
            ACTION_MEDIA_MOUNTED -> {
                LogUtils.i(TAG, "处理USB设备挂载: $devicePath")
                statusMonitor?.recordUsbStatus("MOUNTED", devicePath)
                handleUsbMounted(devicePath)
            }

            ACTION_USB_DETACHED,
            ACTION_MEDIA_UNMOUNTED -> {
                LogUtils.i(TAG, "处理USB设备卸载: $devicePath")
                statusMonitor?.recordUsbStatus("UNMOUNTED", devicePath)
                handleUsbUnmounted(devicePath)
            }
        }
    }
    
    /**
     * 处理USB设备挂载
     */
    private fun handleUsbMounted(path: String) {
        threadPool.submit {
            try {
                val usbDir = File(path)
                if (usbDir.exists() && usbDir.isDirectory) {
                    // 检查是否包含upgrade_task_info.json文件
                    val upgradeTaskFile = File(usbDir, UPGRADE_TASK_FILE)
                    if (upgradeTaskFile.exists()) {
                        LogUtils.i(TAG, "发现upgrade_task_info.json文件: ${upgradeTaskFile.absolutePath}")
                        updateNotification("发现升级任务配置文件")

                        // 记录USB状态 - 发现upgrade_task_info.json
                        statusMonitor?.recordUsbStatus("UPGRADE_TASK_FOUND", path, true)

                        // 分析升级任务
                        upgradeTaskAnalyzer?.analyzeUpgradeTask(usbDir)
                    } else {
                        LogUtils.d(TAG, "USB设备中未发现upgrade_task_info.json文件: $path")
                    }
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "处理USB挂载事件失败: ${e.message}")
                updateNotification("处理USB设备时出错")
            }
        }
    }
    
    /**
     * 处理USB设备卸载
     */
    private fun handleUsbUnmounted(path: String) {
        LogUtils.i(TAG, "USB设备已卸载: $path")
        updateNotification("USB设备已移除")
        
        // 取消相关的升级任务
        upgradeTaskAnalyzer?.cancelTasksForUsbPath(path)
    }
    
    /**
     * 检查已挂载的USB设备
     */
    private fun checkExistingUsbDevices() {
        threadPool.submit {
            try {
                // 检查常见的USB挂载点
                val mountPoints = arrayOf(
                    "/mnt/usb",
                    "/mnt/usbdisk",
                    "/storage/usb",
                    "/mnt/media_rw"
                )
                
                for (mountPoint in mountPoints) {
                    val dir = File(mountPoint)
                    if (dir.exists() && dir.isDirectory) {
                        dir.listFiles()?.forEach { subDir ->
                            if (subDir.isDirectory) {
                                val upgradeTaskFile = File(subDir, UPGRADE_TASK_FILE)
                                if (upgradeTaskFile.exists()) {
                                    LogUtils.i(TAG, "启动时发现已挂载的升级任务: ${subDir.absolutePath}")
                                    handleUsbMounted(subDir.absolutePath)
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "检查已挂载USB设备失败: ${e.message}")
            }
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "USB检测服务",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "监听USB设备插拔和升级任务"
            setShowBadge(false)
        }
        
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.createNotificationChannel(channel)
    }
    
    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("USB检测服务")
            .setContentText("正在监听USB设备插拔")
            .setSmallIcon(android.R.drawable.ic_menu_manage)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    /**
     * 更新通知内容
     */
    private fun updateNotification(content: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("USB检测服务")
            .setContentText(content)
            .setSmallIcon(android.R.drawable.ic_menu_manage)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
        
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
}
