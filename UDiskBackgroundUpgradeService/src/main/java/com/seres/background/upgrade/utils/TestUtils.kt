package com.seres.background.upgrade.utils

import android.content.Context
import com.google.gson.Gson
import com.seres.background.upgrade.model.*
import com.seres.background.upgrade.monitor.StatusMonitor
import java.io.File

/**
 * 测试工具类
 * 用于生成测试数据和验证功能
 */
object TestUtils {
    
    private val gson = Gson()
    
    /**
     * 生成示例upgrade_task_info.json内容
     */
    fun generateSampleUpgradeTaskJson(): String {
        val upgradeTaskInfo = UpgradeTaskInfo(
            edition = Edition(prettyVersion = "F1L-ZUHE-718"),
            dpPackage = DpPackage(
                vin = "LM8F*********0852",
                pkgId = "767361430406012928",
                pkgInfo = PkgInfo(
                    pkgVer = "v1.0",
                    dcList = listOf(
                        DcItem(
                            domain = 2,
                            path = "https://xxx.15.C_250701_8224_00.zip",
                            zipSizeInByte = 2049029,
                            nodeAddr = "0x0304",
                            softVer = "SW:1.15.C_250701_8224_00",
                            pkgType = 0,
                            sha256 = ""
                        )
                    ),
                    ecuList = listOf(
                        EcuItem(
                            domain = 2,
                            ecuId = "3880101-RR01-8224",
                            nodeAddr = "0x0304",
                            seamless = 1
                        )
                    ),
                    rules = Rules(
                        version = 1,
                        expr = Expr(
                            preInstall = PreInstall(
                                elms = listOf(
                                    Element(`var` = "soc", `val` = "15"),
                                    Element(`var` = "spd", `val` = "3")
                                )
                            )
                        )
                    )
                )
            )
        )

        return gson.toJson(upgradeTaskInfo)
    }
    
    /**
     * 生成示例升级任务JSON（旧格式）
     */
    fun generateSampleLegacyUpgradeTaskJson(): String {
        val upgradeTask = UpgradeTask(
            version = "1.0",
            sharePath = "/ota_share/",
            packageInfo = listOf(
                PackageInfo(
                    ecuName = "HPCM",
                    seamless = true,
                    packagePath = "/ota_share/HPCM_v1.1.0.zip",
                    packageSize = 1024000,
                    packageMd5 = "d41d8cd98f00b204e9800998ecf8427e"
                ),
                PackageInfo(
                    ecuName = "ZCUF",
                    seamless = false,
                    packagePath = "/ota_share/ZCUF_v2.1.0.zip",
                    packageSize = 2048000,
                    packageMd5 = "098f6bcd4621d373cade4e832627b4f6"
                )
            ),
            preconditions = Preconditions(
                powerBatteryThreshold = 15,
                batteryThreshold = 65
            ),
            preferences = mapOf(
                "autoReboot" to true,
                "backupBeforeUpgrade" to true
            )
        )
        
        return gson.toJson(upgradeTask)
    }
    
    /**
     * 生成示例资产信息
     */
    fun generateSampleInventoryInfo(): List<InventoryInfo> {
        return listOf(
            InventoryInfo(
                partNumber = "PN001",
                softwareVersion = "1.0.0",
                supplierCode = "SUP001",
                ecuName = "HPCM",
                serialNumber = "SN001",
                hardwareVersion = "HW1.0",
                bootloaderVersion = "BL1.0",
                backupVersion = "BK1.0"
            ),
            InventoryInfo(
                partNumber = "PN002",
                softwareVersion = "2.0.0",
                supplierCode = "SUP002",
                ecuName = "ZCUF",
                serialNumber = "SN002",
                hardwareVersion = "HW2.0",
                bootloaderVersion = "BL2.0",
                backupVersion = "BK2.0"
            ),
            InventoryInfo(
                partNumber = "PN003",
                softwareVersion = "1.5.0",
                supplierCode = "SUP003",
                ecuName = "BCM",
                serialNumber = "SN003",
                hardwareVersion = "HW1.5",
                bootloaderVersion = "BL1.5",
                backupVersion = "BK1.5"
            )
        )
    }
    
    /**
     * 验证upgrade_task_info.json格式
     */
    fun validateUpgradeTaskJson(jsonString: String): Boolean {
        return try {
            val upgradeTaskInfo = gson.fromJson(jsonString, UpgradeTaskInfo::class.java)
            upgradeTaskInfo.dpPackage.pkgId.isNotEmpty() &&
            upgradeTaskInfo.dpPackage.pkgInfo.dcList.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 验证升级任务JSON格式（旧格式）
     */
    fun validateLegacyUpgradeTaskJson(jsonString: String): Boolean {
        return try {
            val task = gson.fromJson(jsonString, UpgradeTask::class.java)
            task.packageInfo.isNotEmpty() && task.sharePath.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 打印格式化的JSON
     */
    fun printFormattedJson(jsonString: String): String {
        return try {
            val jsonObject = gson.fromJson(jsonString, Any::class.java)
            gson.newBuilder().setPrettyPrinting().create().toJson(jsonObject)
        } catch (e: Exception) {
            jsonString
        }
    }

    /**
     * 创建测试用的upgrade_task_info.json文件
     */
    fun createTestUpgradeTaskFile(targetDir: File): Boolean {
        return try {
            val upgradeTaskFile = File(targetDir, "upgrade_task_info.json")
            val upgradeTaskJson = generateSampleUpgradeTaskJson()
            upgradeTaskFile.writeText(upgradeTaskJson)
            LogUtils.i("TestUtils", "测试upgrade_task_info.json文件已创建: ${upgradeTaskFile.absolutePath}")
            true
        } catch (e: Exception) {
            LogUtils.e("TestUtils", "创建测试upgrade_task_info.json文件失败: ${e.message}")
            false
        }
    }

    /**
     * 创建测试用的升级包文件
     */
    fun createTestUpgradePackages(targetDir: File): Boolean {
        return try {
            // 创建示例升级包文件
            val packageFiles = listOf(
                "xxx.15.C_250701_8224_00.zip"
            )

            for (fileName in packageFiles) {
                val packageFile = File(targetDir, fileName)
                // 创建一个简单的测试文件内容
                val testContent = "Test upgrade package: $fileName\nCreated at: ${System.currentTimeMillis()}\nSample upgrade content"
                packageFile.writeText(testContent)
                LogUtils.i("TestUtils", "测试升级包文件已创建: ${packageFile.absolutePath}")
            }

            true
        } catch (e: Exception) {
            LogUtils.e("TestUtils", "创建测试升级包文件失败: ${e.message}")
            false
        }
    }

    /**
     * 获取调试信息
     */
    fun getDebugInfo(context: Context): String {
        val statusMonitor = StatusMonitor(context)
        val debugInfo = StringBuilder()

        debugInfo.append("=== 后台升级服务调试信息 ===\n")
        debugInfo.append("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}\n")
        debugInfo.append("PID: ${android.os.Process.myPid()}\n\n")

        // 状态文件路径
        // debugInfo.append("=== 状态文件路径 ===\n")
        // val statusPaths = statusMonitor.getStatusFilePaths()
        // statusPaths.forEach { (key, path) ->
        //     debugInfo.append("$key: $path\n")
        // }
        // debugInfo.append("\n")

        // 服务状态
        debugInfo.append("=== 服务状态 ===\n")
        val serviceStatus = statusMonitor.readServiceStatus()
        if (serviceStatus != null) {
            debugInfo.append("服务名: ${serviceStatus.serviceName}\n")
            debugInfo.append("运行状态: ${serviceStatus.isRunning}\n")
            debugInfo.append("启动时间: ${serviceStatus.startTime}\n")
            debugInfo.append("最后更新: ${serviceStatus.lastUpdateTime}\n")
            debugInfo.append("PID: ${serviceStatus.pid}\n")
        } else {
            debugInfo.append("未找到服务状态信息\n")
        }
        debugInfo.append("\n")

        // USB状态
        debugInfo.append("=== USB状态 ===\n")
        val usbStatus = statusMonitor.readUsbStatus()
        if (usbStatus != null) {
            debugInfo.append("动作: ${usbStatus.action}\n")
            debugInfo.append("设备路径: ${usbStatus.devicePath}\n")
            debugInfo.append("时间戳: ${usbStatus.timestamp}\n")
            debugInfo.append("包含device_list: ${usbStatus.hasDeviceList}\n")
            debugInfo.append("设备数量: ${usbStatus.deviceCount}\n")
            debugInfo.append("升级包数量: ${usbStatus.packageCount}\n")
        } else {
            debugInfo.append("未找到USB状态信息\n")
        }
        debugInfo.append("\n")

        // 任务状态
        debugInfo.append("=== 任务状态 ===\n")
        val taskStatus = statusMonitor.readTaskStatus()
        if (taskStatus != null) {
            debugInfo.append("任务ID: ${taskStatus.taskId}\n")
            debugInfo.append("状态: ${taskStatus.status}\n")
            debugInfo.append("创建时间: ${taskStatus.createTime}\n")
            debugInfo.append("更新时间: ${taskStatus.updateTime}\n")
            debugInfo.append("USB路径: ${taskStatus.usbPath}\n")
            debugInfo.append("升级包数量: ${taskStatus.packageCount}\n")
            if (taskStatus.errorMessage.isNotEmpty()) {
                debugInfo.append("错误信息: ${taskStatus.errorMessage}\n")
            }
        } else {
            debugInfo.append("未找到任务状态信息\n")
        }
        debugInfo.append("\n")

        // 心跳检查
        debugInfo.append("=== 心跳检查 ===\n")
        val heartbeat = statusMonitor.checkHeartbeat()
        if (heartbeat != null) {
            debugInfo.append("心跳: $heartbeat\n")
        } else {
            debugInfo.append("未找到心跳信息\n")
        }
        debugInfo.append("\n")

        // 升级任务JSON
        debugInfo.append("=== 升级任务JSON ===\n")
        val latestTaskJson = statusMonitor.readLatestUpgradeTaskJson()
        if (latestTaskJson != null) {
            debugInfo.append("最新升级任务JSON:\n")
            debugInfo.append(printFormattedJson(latestTaskJson))
            debugInfo.append("\n")
        } else {
            debugInfo.append("未找到升级任务JSON\n")
        }

        // 升级任务文件列表
        val taskJsonFiles = statusMonitor.getUpgradeTaskJsonFiles()
        if (taskJsonFiles.isNotEmpty()) {
            debugInfo.append("历史升级任务文件:\n")
            taskJsonFiles.forEach { fileName ->
                debugInfo.append("  - $fileName\n")
            }
        } else {
            debugInfo.append("无历史升级任务文件\n")
        }

        return debugInfo.toString()
    }
}
