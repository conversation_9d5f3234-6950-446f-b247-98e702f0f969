~ $ adb logcat | grep BackgroundUpgrade
07-18 14:12:49.436  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true      07-18 14:13:10.904  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true      07-18 14:13:32.630  2560 11922 I BackgroundUpgrade-StatusMonitor: 状态监控器初始化完成，状态目录: /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status  07-18 14:13:40.900  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true      07-18 14:14:10.902  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true      07-18 14:14:40.899  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true      07-18 14:15:10.899  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true      07-18 14:15:40.900  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true      07-18 14:16:10.904  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true      07-18 14:16:40.901  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true
07-18 14:16:42.874 17606 17606 D ZeroTermux--ClipBoardUtil: registerClipEvents Clipboard text is:BackgroundUpgrade  07-18 14:16:42.875 17606 17606 D ZeroTermux--FileIOUtils: setClipBoardString ClipBoard save data :{"data":{"list":[{"index":2,"name":"BackgroundUpgrade"},{"index":1,"name":"# The main termux repository, behind cloudflare cache:\n#deb https://packages-cf.termux.org/apt/termux-main/ stable main\ndeb https://mirrors.bfsu.edu.cn/termux/termux-packages-24 stable main\n# The main termux repository:\n# deb https://packages.termux.org/apt/termux-main/ stable main\n"},{"index":0,"name":"--scan"}]}}
07-18 14:16:42.977 17606 17606 D ZeroTermux--ClipBoardUtil: registerClipEvents Clipboard text is:BackgroundUpgrade
07-18 14:17:10.901  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true
07-18 14:17:37.280  2560  2560 I BackgroundUpgrade-UsbDeviceReceiver: 存储设备挂载: /storage/E58D-A352
07-18 14:17:37.285  2560  2560 I BackgroundUpgrade-UsbDetectionService: UsbDetectionService onStartCommand: com.seres.background.upgrade.MEDIA_MOUNTED - flags: 0, startId: 8
07-18 14:17:37.291  2560  2560 I BackgroundUpgrade-UsbDetectionService: USB检测服务前台通知已更新
07-18 14:17:37.291  2560  2560 I BackgroundUpgrade-UsbDetectionService: 处理USB设备挂载: /storage/E58D-A352
07-18 14:17:37.295  2560  2560 I BackgroundUpgrade-StatusMonitor: USB状态已记录: MOUNTED -> /storage/E58D-A352
07-18 14:17:37.303  2560 16919 I BackgroundUpgrade-UsbDetectionService: 发现device_list.json文件: /storage/E58D-A352/device_list.json
07-18 14:17:37.308  2560 16919 I BackgroundUpgrade-StatusMonitor: USB状态已记录: DEVICE_LIST_FOUND -> /storage/E58D-A352
07-18 14:17:37.309  2560 16919 I BackgroundUpgrade-DeviceListAnalyzer: 开始分析升级任务: /storage/E58D-A352
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 读取到device_list.json内容: {
07-18 14:17:37.312  2560 16 919 D BackgroundUpgrade-DeviceListAnalyzer:   "device_list": {
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:     "cdc_domain": [
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       {
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ecu_name": "device1",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ip_addr": "0.0.0.0",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "phy_addr": "0x1234"
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       },
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       {
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ecu_name": "device2",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ip_addr": "0.0.0.0",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "phy_addr": "0x1235"
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       }
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:     ],
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:     "mdc_domain": [
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       {
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ecu_name": "device3",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ip_addr": "0.0.0.0",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "phy_addr": "0x1236"
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       },
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       {
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ecu_name": "device4",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ip_addr": "0.0.0.0",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "phy_addr": "0x1237"
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       }
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:     ],
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:     "vdc_domain": [
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       {
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ecu_name": "device5",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ip_addr": "0.0.0.0",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "phy_addr": "0x1238"
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       },
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       {
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ecu_name": "device6",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "ip_addr": "0.0.0.0",
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:         "phy_addr": "0x1239"
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:       }
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:     ]
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer:   }
07-18 14:17:37.312  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: }
07-18 14:17:37.313  2560 16919 I BackgroundUpgrade-DeviceListAnalyzer: 检测到新格式的device_list.json
07-18 14:17:37.314  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: CDC域设备数量: 2
07-18 14:17:37.314  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: MDC域设备数量: 2
07-18 14:17:37.314  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: VDC域设备数量: 2
07-18 14:17:37.314  2560 16919 I BackgroundUpgrade-DeviceListAnalyzer: 总设备数量: 6
07-18 14:17:37.318  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 设备: device1, IP: 0.0.0.0, 物理地址: 0x1234
07-18 14:17:37.319  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 设备: device2, IP: 0.0.0.0, 物理地址: 0x1235
07-18 14:17:37.319  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 设备: device3, IP: 0.0.0.0, 物理地址: 0x1236
07-18 14:17:37.319  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 设备: device4, IP: 0.0.0.0, 物理地址: 0x1237
07-18 14:17:37.319  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 设备: device5, IP: 0.0.0.0, 物理地址: 0x1238
07-18 14:17:37.319  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 设备: device6, IP: 0.0.0.0, 物理地址: 0x1239
07-18 14:17:37.320  2560 16919 I BackgroundUpgrade-DeviceListAnalyzer: 发现 6 个设备，开始扫描升级包
07-18 14:17:37.411  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: USB目录下找到 6 个升级包文件
07-18 14:17:37.414  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 找到升级包: device1 -> device1_firmware_1.1.0.zip
07-18 14:17:37.415  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 找到升级包: device2 -> device2_firmware_1.1.0.zip
07-18 14:17:37.417  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 找到升级包: device3 -> device3_firmware_1.1.0.zip
07-18 14:17:37.418  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 找到升级包: device4 -> device4_firmware_1.1.0.zip
07-18 14:17:37.420  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 找到升级包: device5 -> device5_firmware_1.1.0.zip
07-18 14:17:37.422  2560 16919 D BackgroundUpgrade-DeviceListAnalyzer: 找到升级包: device6 -> device6_firmware_1.1.0.zip
07-18 14:17:37.422  2560 16919 I BackgroundUpgrade-StatusMonitor: USB状态已记录: ANALYSIS_COMPLETE -> /storage/E58D-A352
07-18 14:17:37.423  2560 16919 I BackgroundUpgrade-DeviceListAnalyzer: 开始处理升级任务，任务管理器已就绪
07-18 14:17:37.423  2560 16919 I BackgroundUpgrade-UpgradeTaskManager: 开始处理升级任务: UPGRADE_1752819457423_2283 ，包含 6 个升级包
07-18 14:17:37.424  2560 16919 D BackgroundUpgrade-UpgradeTaskManager: 任务详情: {"packageInfo":[{"ecuName":"device1","packageMd5":"d41d8cd98f00b204e9800998ecf8427e","packagePath":"/ota_share/device1_firmware_1.1.0.zip","packageSize":0,"seamless":true},{"ecuName":"device2","packageMd5":"d41d8cd98f00b204e9800998ecf8427e","packagePath":"/ota_share/device2_firmware_1.1.0.zip","packageSize":0,"seamless":true},{"ecuName":"device3","packageMd5":"d41d8cd98f00b204e9800998ecf8427e","packagePath":"/ota_share/device3_firmware_1.1.0.zip","packageSize":0,"seamless":true},{"ecuName":"device4","packageMd5":"d41d8cd98f00b204e9800998ecf8427e","packagePath":"/ota_share/device4_firmware_1.1.0.zip","packageSize":0,"seamless":true},{"ecuName":"device5","packageMd5":"d41d8cd98f00b204e9800998ecf8427e","packagePath":"/ota_share/device5_firmware_1.1.0.zip","packageSize":0,"seamless":true},{"ecuName":"device6","packageMd5":"d41d8cd98f00b204e9800998ecf8427e","packagePath":"/ota_share/device6_firmware_1.1.0.zip","packageSize":0,"seamless":true}],"preconditions":{"batteryThreshold":65,"powerBatteryThreshold":15},"preferences":{},"sharePath":"/ota_share/","version":"1.0"}
07-18 14:17:37.425  2560 16919 I BackgroundUpgrade-DeviceListAnalyzer: 升级任务已提交给任务管理器
07-18 14:17:37.425  2560 16946 D BackgroundUpgrade-UpgradeTaskManager: 任务状态更新: UPGRADE_1752819457423_2283 -> COPYING
07-18 14:17:37.425  2560 16919 I BackgroundUpgrade-DeviceListAnalyzer: 升级任务分析完成，包含 6 个升级包
07-18 14:17:37.470  2560 16946 E BackgroundUpgrade-UpgradeTaskManager: 拷贝升级文件失败: /ota_share/device1_firmware_1.1.0.zip: open failed: ENOENT (No such file or directory)
07-18 14:17:37.470  2560 16946 D BackgroundUpgrade-UpgradeTaskManager: 任务状态更新: UPGRADE_1752819457423_2283 -> FAILED
07-18 14:17:37.471  2560 16946 E BackgroundUpgrade-UpgradeTaskManager: 升级任务文件拷贝失败: UPGRADE_1752819457423_2283
07-18 14:17:40.902  2560  2605 D BackgroundUpgrade-WatchdogService: 服务状态检查 - 主服务: true, USB服务: true
07-18 14:17:51.411  2560  2560 I BackgroundUpgrade-UsbDeviceReceiver: 存储设备卸载: /storage/E58D-A352
07-18 14:17:51.416  2560  2560 I BackgroundUpgrade-UsbDetectionService: UsbDetectionService onStartCommand: com.seres.background.upgrade.MEDIA_UNMOUNTED - flags: 0, startId: 9
07-18 14:17:51.421  2560  2560 I BackgroundUpgrade-UsbDetectionService: USB检测服务前台通知已更新
07-18 14:17:51.422  2560  2560 I BackgroundUpgrade-UsbDetectionService: 处理USB设备卸载: /storage/E58D-A352
07-18 14:17:51.423  2560  2560 I BackgroundUpgrade-StatusMonitor: USB状态已记录: UNMOUNTED -> /storage/E58D-A352
07-18 14:17:51.424  2560  2560 I BackgroundUpgrade-UsbDetectionService: USB设备已卸载: /storage/E58D-A352
07-18 14:17:51.429  2560  2560 I BackgroundUpgrade-DeviceListAnalyzer: 取消USB路径相关的任务: /storage/E58D-A352
07-18 14:17:51.429  2560  2560 I BackgroundUpgrade-UpgradeTaskManager: 取消USB路径相关的任务: /storage/E58D-A352
07-18 14:17:51.430  2560  2560 D BackgroundUpgrade-UpgradeTaskManager: 任务状态更新: UPGRADE_1752819457423_2283 -> CANCELLED
07-18 14:17:51.430  2560  2560 I BackgroundUpgrade-UpgradeTaskManager: 已取消任务: UPGRADE_1752819457423_2283
07-18 14:17:51.995  2560  2560 I BackgroundUpgrade-UsbDeviceReceiver: 存储设备卸载: /storage/E58D-A352
07-18 14:17:52.002  2560  2560 I BackgroundUpgrade-UsbDetectionService: UsbDetectionService onStartCommand: com.seres.background.upgrade.MEDIA_UNMOUNTED - flags: 0, startId: 10
07-18 14:17:52.005  2560  2560 I BackgroundUpgrade-UsbDetectionService: USB检测服务前台通知已更新
07-18 14:17:52.005  2560  2560 I BackgroundUpgrade-UsbDetectionService: 处理USB设备卸载: /storage/E58D-A352
07-18 14:17:52.006  2560  2560 I BackgroundUpgrade-StatusMonitor: USB状态已记录: UNMOUNTED -> /storage/E58D-A352
07-18 14:17:52.006  2560  2560 I BackgroundUpgrade-UsbDetectionService: USB设备已卸载: /storage/E58D-A352
07-18 14:17:52.008  2560  2560 I BackgroundUpgrade-DeviceListAnalyzer: 取消USB路径相关的任务: /storage/E58D-A352
07-18 14:17:52.009  2560  2560 I BackgroundUpgrade-UpgradeTaskManager: 取消USB路径相关的任务: /storage/E58D-A352