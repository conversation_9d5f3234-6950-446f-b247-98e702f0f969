package com.seres.usb.upgrade.storage

import android.content.Context
import android.os.Environment
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import com.seres.usb.upgrade.utils.LogUtils
import com.seres.usb.upgrade.utils.PermissionUtils
import java.io.File

/**
 * 存储状态管理器
 * 管理外部存储器状态，包括U盘检测和状态监控
 */
class StorageStatusManager(private val context: Context) {
    
    private val TAG = "StorageStatusManager"
    private var statusListener: StorageStatusListener? = null
    
    /**
     * 存储设备信息
     */
    data class StorageDeviceInfo(
        val path: String,
        val description: String,
        val state: String,
        val isRemovable: Boolean,
        val isPrimary: Boolean,
        val totalSpace: Long,
        val freeSpace: Long,
        val usedSpace: Long
    ) {
        val totalSpaceGB: Double get() = totalSpace / (1024.0 * 1024.0 * 1024.0)
        val freeSpaceGB: Double get() = freeSpace / (1024.0 * 1024.0 * 1024.0)
        val usedSpaceGB: Double get() = usedSpace / (1024.0 * 1024.0 * 1024.0)
        val usagePercentage: Int get() = if (totalSpace > 0) ((usedSpace * 100) / totalSpace).toInt() else 0
    }
    
    /**
     * 存储状态监听器
     */
    interface StorageStatusListener {
        fun onStorageDeviceAdded(device: StorageDeviceInfo)
        fun onStorageDeviceRemoved(path: String)
        fun onStorageStatusChanged(devices: List<StorageDeviceInfo>)
    }
    
    /**
     * 设置状态监听器
     */
    fun setStatusListener(listener: StorageStatusListener) {
        this.statusListener = listener
    }
    
    /**
     * 获取所有存储设备信息
     */
    fun getAllStorageDevices(): List<StorageDeviceInfo> {
        val devices = mutableListOf<StorageDeviceInfo>()

        // 检查权限
        if (!PermissionUtils.hasStoragePermission(context)) {
            LogUtils.w(TAG, "No storage permission, returning empty device list")
            return devices
        }

        try {
            val storageManager = context.getSystemService(Context.STORAGE_SERVICE) as StorageManager
            val volumes = storageManager.storageVolumes

            for (volume in volumes) {
                val deviceInfo = getStorageDeviceInfo(volume)
                if (deviceInfo != null) {
                    devices.add(deviceInfo)
                    LogUtils.d(TAG, "Found storage device: ${deviceInfo.description} at ${deviceInfo.path}")
                }
            }

            // 添加内部存储信息
            val internalStorage = getInternalStorageInfo()
            if (internalStorage != null) {
                devices.add(0, internalStorage) // 添加到列表开头
            }

        } catch (e: Exception) {
            LogUtils.e(TAG, "Error getting storage devices: ${e.message}")
        }

        return devices
    }
    
    /**
     * 获取可移动存储设备（U盘等）
     */
    fun getRemovableStorageDevices(): List<StorageDeviceInfo> {
        return getAllStorageDevices().filter { it.isRemovable && it.state == "mounted" }
    }
    
    /**
     * 检查是否有U盘插入
     */
    fun hasUsbDeviceConnected(): Boolean {
        return getRemovableStorageDevices().isNotEmpty()
    }
    
    /**
     * 获取第一个可用的U盘路径
     */
    fun getFirstUsbDevicePath(): String? {
        val usbDevices = getRemovableStorageDevices()
        return if (usbDevices.isNotEmpty()) usbDevices[0].path else null
    }
    
    /**
     * 获取存储设备详细信息
     */
    private fun getStorageDeviceInfo(volume: StorageVolume): StorageDeviceInfo? {
        return try {
            val path = getVolumePath(volume)
            if (path != null) {
                val file = File(path)
                val totalSpace = if (file.exists()) file.totalSpace else 0L
                val freeSpace = if (file.exists()) file.freeSpace else 0L
                val usedSpace = totalSpace - freeSpace
                
                StorageDeviceInfo(
                    path = path,
                    description = getVolumeDescription(volume),
                    state = volume.state ?: "unknown",
                    isRemovable = volume.isRemovable,
                    isPrimary = volume.isPrimary,
                    totalSpace = totalSpace,
                    freeSpace = freeSpace,
                    usedSpace = usedSpace
                )
            } else null
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error getting device info for volume: ${e.message}")
            null
        }
    }
    
    /**
     * 获取内部存储信息
     */
    private fun getInternalStorageInfo(): StorageDeviceInfo? {
        return try {
            val internalDir = Environment.getDataDirectory()
            val totalSpace = internalDir.totalSpace
            val freeSpace = internalDir.freeSpace
            val usedSpace = totalSpace - freeSpace
            
            StorageDeviceInfo(
                path = internalDir.absolutePath,
                description = "内部存储",
                state = "mounted",
                isRemovable = false,
                isPrimary = true,
                totalSpace = totalSpace,
                freeSpace = freeSpace,
                usedSpace = usedSpace
            )
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error getting internal storage info: ${e.message}")
            null
        }
    }
    
    /**
     * 获取存储卷路径
     */
    private fun getVolumePath(volume: StorageVolume): String? {
        return try {
            // 使用反射获取路径，因为getPath()在API 30+中被隐藏
            val getPathMethod = volume.javaClass.getMethod("getPath")
            getPathMethod.invoke(volume) as String
        } catch (e: Exception) {
            LogUtils.w(TAG, "Failed to get volume path: ${e.message}")
            // 尝试其他方法获取路径
            try {
                val getDirectoryMethod = volume.javaClass.getMethod("getDirectory")
                val directory = getDirectoryMethod.invoke(volume) as File?
                directory?.absolutePath
            } catch (e2: Exception) {
                LogUtils.w(TAG, "Failed to get volume directory: ${e2.message}")
                null
            }
        }
    }
    
    /**
     * 获取存储卷描述
     */
    private fun getVolumeDescription(volume: StorageVolume): String {
        return try {
            val description = volume.getDescription(context)
            if (description.isNullOrEmpty()) {
                if (volume.isRemovable) "可移动存储" else "本地存储"
            } else {
                description
            }
        } catch (e: Exception) {
            LogUtils.w(TAG, "Failed to get volume description: ${e.message}")
            if (volume.isRemovable) "可移动存储" else "本地存储"
        }
    }
    
    /**
     * 刷新存储状态
     */
    fun refreshStorageStatus() {
        try {
            val devices = getAllStorageDevices()
            statusListener?.onStorageStatusChanged(devices)
            LogUtils.d(TAG, "Storage status refreshed, found ${devices.size} devices")
        } catch (e: Exception) {
            LogUtils.e(TAG, "Error refreshing storage status: ${e.message}")
        }
    }
    
    /**
     * 格式化存储空间大小
     */
    fun formatStorageSize(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> String.format("%.2f MB", bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> String.format("%.2f KB", bytes / 1024.0)
            else -> "$bytes B"
        }
    }
    
    /**
     * 检查路径是否可访问
     */
    fun isPathAccessible(path: String): Boolean {
        return try {
            val file = File(path)
            file.exists() && file.canRead()
        } catch (e: Exception) {
            LogUtils.w(TAG, "Path not accessible: $path - ${e.message}")
            false
        }
    }
}
