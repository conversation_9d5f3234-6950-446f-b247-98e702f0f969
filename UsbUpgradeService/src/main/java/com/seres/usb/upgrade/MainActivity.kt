package com.seres.usb.upgrade

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.seres.usb.upgrade.adapter.StorageDeviceAdapter
import com.seres.usb.upgrade.databinding.ActivityMainBinding
import com.seres.usb.upgrade.storage.StorageStatusManager
import com.seres.usb.upgrade.utils.LogUtils

/**
 * USB升级服务主界面
 * 显示存储设备状态和升级任务信息
 */
class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var storageStatusManager: StorageStatusManager
    private lateinit var storageDeviceAdapter: StorageDeviceAdapter
    
    // 权限请求启动器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            checkManageExternalStoragePermission()
        } else {
            showPermissionDeniedDialog()
        }
    }

    // MANAGE_EXTERNAL_STORAGE权限请求启动器
    private val manageExternalStorageLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (Environment.isExternalStorageManager()) {
                initStorageManager()
            } else {
                showManageStoragePermissionDeniedDialog()
            }
        } else {
            initStorageManager()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        LogUtils.d(TAG, "MainActivity onCreate")
        
        setupUI()
        checkPermissions()
    }
    
    /**
     * 设置UI界面
     */
    private fun setupUI() {
        // 设置RecyclerView
        storageDeviceAdapter = StorageDeviceAdapter(this)
        binding.recyclerViewStorageDevices.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = storageDeviceAdapter
        }
        
        // 设置刷新按钮
        binding.btnRefreshStorage.setOnClickListener {
            refreshStorageDevices()
        }
    }
    
    /**
     * 检查权限
     */
    private fun checkPermissions() {
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13+ 使用新的媒体权限
                checkAndroid13Permissions()
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                // Android 11+ 需要MANAGE_EXTERNAL_STORAGE权限
                checkAndroid11Permissions()
            }
            else -> {
                // Android 10及以下使用传统权限
                checkLegacyPermissions()
            }
        }
    }

    /**
     * 检查Android 13+权限
     */
    private fun checkAndroid13Permissions() {
        val permissions = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
        }
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_VIDEO)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
        }
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_AUDIO)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.READ_MEDIA_AUDIO)
        }

        if (permissions.isNotEmpty()) {
            permissionLauncher.launch(permissions.toTypedArray())
        } else {
            checkManageExternalStoragePermission()
        }
    }

    /**
     * 检查Android 11+权限
     */
    private fun checkAndroid11Permissions() {
        val permissions = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
        }

        if (permissions.isNotEmpty()) {
            permissionLauncher.launch(permissions.toTypedArray())
        } else {
            checkManageExternalStoragePermission()
        }
    }

    /**
     * 检查传统权限（Android 10及以下）
     */
    private fun checkLegacyPermissions() {
        val permissions = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
        }
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }

        if (permissions.isNotEmpty()) {
            permissionLauncher.launch(permissions.toTypedArray())
        } else {
            initStorageManager()
        }
    }

    /**
     * 检查MANAGE_EXTERNAL_STORAGE权限
     */
    private fun checkManageExternalStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (!Environment.isExternalStorageManager()) {
                showManageStoragePermissionDialog()
            } else {
                initStorageManager()
            }
        } else {
            initStorageManager()
        }
    }
    
    /**
     * 初始化存储管理器
     */
    private fun initStorageManager() {
        storageStatusManager = StorageStatusManager(this)
        refreshStorageDevices()
    }
    
    /**
     * 刷新存储设备列表
     */
    private fun refreshStorageDevices() {
        if (::storageStatusManager.isInitialized) {
            val devices = storageStatusManager.getAllStorageDevices()
            storageDeviceAdapter.updateDevices(devices)
            
            binding.tvStorageStatus.text = "已检测到 ${devices.size} 个存储设备"
            LogUtils.d(TAG, "刷新存储设备列表，共 ${devices.size} 个设备")
        }
    }
    
    /**
     * 显示管理存储权限对话框
     */
    private fun showManageStoragePermissionDialog() {
        AlertDialog.Builder(this)
            .setTitle("需要存储管理权限")
            .setMessage("为了访问USB设备和外部存储，应用需要\"所有文件访问权限\"。请在设置中授予此权限。")
            .setPositiveButton("去设置") { _, _ ->
                requestManageExternalStoragePermission()
            }
            .setNegativeButton("取消") { _, _ ->
                Toast.makeText(this, "没有存储权限，无法正常工作", Toast.LENGTH_LONG).show()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 请求MANAGE_EXTERNAL_STORAGE权限
     */
    private fun requestManageExternalStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            try {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                    data = Uri.parse("package:$packageName")
                }
                manageExternalStorageLauncher.launch(intent)
            } catch (e: Exception) {
                LogUtils.e(TAG, "无法打开存储权限设置页面", e)
                // 如果无法打开应用特定的设置页面，尝试打开通用设置页面
                try {
                    val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
                    manageExternalStorageLauncher.launch(intent)
                } catch (e2: Exception) {
                    LogUtils.e(TAG, "无法打开通用存储权限设置页面", e2)
                    Toast.makeText(this, "请手动在设置中授予存储权限", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * 显示权限被拒绝对话框
     */
    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle("权限被拒绝")
            .setMessage("应用需要存储权限才能正常工作，请在设置中手动授予权限。")
            .setPositiveButton("去设置") { _, _ ->
                openAppSettings()
            }
            .setNegativeButton("取消") { _, _ ->
                Toast.makeText(this, "没有权限，功能受限", Toast.LENGTH_LONG).show()
            }
            .show()
    }

    /**
     * 显示管理存储权限被拒绝对话框
     */
    private fun showManageStoragePermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle("存储管理权限被拒绝")
            .setMessage("没有\"所有文件访问权限\"，应用可能无法访问USB设备。是否重新尝试授权？")
            .setPositiveButton("重新授权") { _, _ ->
                requestManageExternalStoragePermission()
            }
            .setNegativeButton("继续使用") { _, _ ->
                initStorageManager()
            }
            .show()
    }

    /**
     * 打开应用设置页面
     */
    private fun openAppSettings() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:$packageName")
            }
            startActivity(intent)
        } catch (e: Exception) {
            LogUtils.e(TAG, "无法打开应用设置页面", e)
            Toast.makeText(this, "请手动在设置中找到应用并授予权限", Toast.LENGTH_LONG).show()
        }
    }

    override fun onResume() {
        super.onResume()
        refreshStorageDevices()
    }

    companion object {
        private const val TAG = "MainActivity"
    }
}
