@echo off
chcp 65001 >nul
echo === Testing Auto-Restart Functionality ===
echo.

echo 1. Check current service status...
adb shell ps | findstr background.upgrade
if %errorlevel% equ 0 (
    echo    [OK] Service is currently running
) else (
    echo    [ERROR] Service is not running, please start it first
    echo    Run start_service.bat first
    pause
    exit /b 1
)
echo.

echo 2. Kill the service process...
echo    Finding and killing service processes...
for /f "tokens=2" %%i in ('adb shell ps ^| findstr background.upgrade ^| findstr -v grep') do (
    echo    Killing PID: %%i
    adb shell kill %%i
)
echo    Service processes killed
echo.

echo 3. Wait 10 seconds for auto-restart...
timeout /t 10 >nul

echo 4. Check if service restarted automatically...
adb shell ps | findstr background.upgrade
if %errorlevel% equ 0 (
    echo    [SUCCESS] Service restarted automatically!
    echo    Auto-restart mechanism is working
) else (
    echo    [FAILED] Service did not restart automatically
    echo    Checking logs for restart attempts...
    adb logcat -d | findstr "BackgroundUpgrade.*restart\|BackgroundUpgrade.*onDestroy\|BackgroundUpgrade.*onCreate" | tail -10
)
echo.

echo 5. Check watchdog service...
adb shell ps | findstr WatchdogService
if %errorlevel% equ 0 (
    echo    [OK] Watchdog service is running
) else (
    echo    [WARNING] Watchdog service not found
)
echo.

echo 6. Check recent logs...
echo    Recent restart-related logs:
adb logcat -d | findstr "BackgroundUpgrade.*restart\|BackgroundUpgrade.*Watchdog\|BackgroundUpgrade.*onDestroy\|BackgroundUpgrade.*onCreate" | tail -15
echo.

echo 7. Final status check...
echo    Current running services:
adb shell dumpsys activity services | findstr BackgroundUpgrade
echo.

echo Test completed!
echo If the service restarted automatically, the auto-restart mechanism is working.
echo If not, check the logs above for error messages.
pause
