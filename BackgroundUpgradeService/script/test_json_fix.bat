@echo off
chcp 65001 >nul
echo === Test JSON Generation Fix ===
echo.

echo 1. Clean up previous test...
adb shell rm -rf /sdcard/test_json_fix/
adb shell rm -f /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/upgrade_task_*.json
adb shell rm -f /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/latest_upgrade_task.json
echo    Previous test data cleaned

echo 2. Create test directory and files...
adb shell mkdir -p /sdcard/test_json_fix/

echo 3. Create device_list.json...
adb shell "cat > /sdcard/test_json_fix/device_list.json << 'EOF'
{
    \"device_list\": {
        \"cdc_domain\": [
            {
                \"ecu_name\": \"device1\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1234\"
            }
        ],
        \"mdc_domain\": [
            {
                \"ecu_name\": \"device2\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1235\"
            }
        ],
        \"vdc_domain\": []
    }
}
EOF"
echo    device_list.json created

echo 4. Create upgrade packages...
adb shell "echo 'Device1 upgrade package content' > /sdcard/test_json_fix/device1.zip"
adb shell "echo 'Device2 upgrade package content' > /sdcard/test_json_fix/device2_firmware.bin"
echo    Upgrade packages created

echo 5. Create possible target directories...
adb shell mkdir -p /sdcard/ota_share/
adb shell mkdir -p /storage/emulated/0/ota_share/
adb shell mkdir -p /storage/emulated/0/Android/data/com.seres.background.upgrade/files/ota_share/
echo    Target directories created

echo 6. Clear previous logs...
adb logcat -c
echo    Logs cleared

echo 7. Trigger USB mount event...
adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/test_json_fix
echo    USB mount event triggered

echo 8. Wait 8 seconds for processing...
timeout /t 8 >nul

echo 9. Check logs for JSON generation...
echo    Recent logs:
adb logcat -d | findstr "UpgradeTaskManager.*JSON\|UpgradeTaskManager.*保存\|UpgradeTaskManager.*升级任务处理"
echo.

echo 10. Check if task JSON was generated...
echo    Latest upgrade task JSON:
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/latest_upgrade_task.json 2>nul
if %errorlevel% equ 0 (
    echo.
    echo    [SUCCESS] Task JSON generated successfully!
) else (
    echo    [INFO] Task JSON not found yet
)
echo.

echo 11. Check all task JSON files...
adb shell ls -la /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/upgrade_task_*.json 2>nul
echo.

echo 12. Check possible target directories...
echo    /sdcard/ota_share/:
adb shell ls -la /sdcard/ota_share/ 2>nul
echo    /storage/emulated/0/ota_share/:
adb shell ls -la /storage/emulated/0/ota_share/ 2>nul
echo    App files ota_share:
adb shell ls -la /storage/emulated/0/Android/data/com.seres.background.upgrade/files/ota_share/ 2>nul
echo.

echo 13. Show debug activity...
adb shell am start -n com.seres.background.upgrade/.DebugActivity
echo    Debug activity started (click to close)

echo.
echo Test completed!
echo The fix should now generate JSON even if file copy fails.
pause
