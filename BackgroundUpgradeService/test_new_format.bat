@echo off
chcp 65001 >nul
echo === Testing New Device List Format ===
echo.

echo 1. Create test USB directory...
adb shell mkdir -p /sdcard/test_usb_new/
echo    Test directory created

echo 2. Create new format device_list.json...
adb shell "cat > /sdcard/test_usb_new/device_list.json << 'EOF'
{
    \"device_list\": {
        \"cdc_domain\": [
            {
                \"ecu_name\": \"device1\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1234\"
            },
            {
                \"ecu_name\": \"device2\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1235\"
            }
        ],
        \"mdc_domain\": [
            {
                \"ecu_name\": \"device3\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1236\"
            },
            {
                \"ecu_name\": \"device4\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1237\"
            }
        ],
        \"vdc_domain\": [
            {
                \"ecu_name\": \"device5\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1238\"
            },
            {
                \"ecu_name\": \"device6\",
                \"ip_addr\": \"0.0.0.0\",
                \"phy_addr\": \"0x1239\"
            }
        ]
    }
}
EOF"
echo    New format device_list.json created

echo 3. Create test upgrade packages...
adb shell "echo 'Device1 upgrade package content' > /sdcard/test_usb_new/device1.zip"
adb shell "echo 'Device2 upgrade package content' > /sdcard/test_usb_new/device2_firmware.bin"
adb shell "echo 'Device3 upgrade package content' > /sdcard/test_usb_new/device3_update.hex"
adb shell "echo 'Device4 upgrade package content' > /sdcard/test_usb_new/device4.img"
adb shell "echo 'Device5 upgrade package content' > /sdcard/test_usb_new/device5_software.tar"
adb shell "echo 'Device6 upgrade package content' > /sdcard/test_usb_new/device6.zip"
echo    Test upgrade packages created

echo 4. Verify files created...
echo    Files in test directory:
adb shell ls -la /sdcard/test_usb_new/
echo.

echo 5. Check device_list.json content...
echo    Content of device_list.json:
adb shell cat /sdcard/test_usb_new/device_list.json
echo.

echo 6. Trigger USB mount event...
adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/test_usb_new
echo    USB mount broadcast sent

echo 7. Wait 5 seconds for processing...
timeout /t 5 >nul

echo 8. Check logs for new format detection...
echo    Recent logs related to device list analysis:
adb logcat -d | findstr "DeviceListAnalyzer.*新格式\|DeviceListAnalyzer.*设备\|DeviceListAnalyzer.*升级包"
echo.

echo 9. Check USB status file...
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/usb_status.json 2>nul
echo.

echo 10. Check if upgrade packages were processed...
echo    Checking /ota_share/ directory:
adb shell ls -la /ota_share/ 2>nul
echo.

echo 11. Check task status...
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/task_status.json 2>nul
echo.

echo Test completed!
echo Check the logs above to see if the new format was detected and processed correctly.
echo Expected: Should detect 6 devices across 3 domains and find matching upgrade packages.
pause
