package com.seres.background.upgrade

import android.app.Activity
import android.os.Bundle
import android.widget.TextView
import com.seres.background.upgrade.utils.TestUtils

/**
 * 调试Activity
 * 隐藏的调试界面，用于查看服务状态
 * 通过ADB命令启动: adb shell am start -n com.seres.background.upgrade/.DebugActivity
 */
class DebugActivity : Activity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 创建简单的文本视图显示调试信息
        val textView = TextView(this).apply {
            textSize = 12f
            setPadding(16, 16, 16, 16)
            text = "正在加载调试信息..."
        }
        setContentView(textView)
        
        // 异步加载调试信息
        Thread {
            val debugInfo = TestUtils.getDebugInfo(this)
            runOnUiThread {
                textView.text = debugInfo
            }
        }.start()
        
        // 5秒后自动关闭
        textView.postDelayed({
            finish()
        }, 5000)
    }
}
