package com.seres.background.upgrade.manager

import com.google.gson.Gson
import com.seres.background.upgrade.model.UpgradeTask
import com.seres.background.upgrade.publisher.S2STaskPublisher
import com.seres.background.upgrade.utils.LogUtils
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors

/**
 * 升级任务管理器
 * 负责处理升级任务的文件拷贝、JSON生成和S2S通信
 */
class UpgradeTaskManager private constructor() {
    
    private val TAG = "UpgradeTaskManager"
    private val gson = Gson()
    private val threadPool = Executors.newCachedThreadPool()
    private val activeTasks = ConcurrentHashMap<String, UpgradeTaskInfo>()
    
    private var s2sTaskPublisher: S2STaskPublisher? = null
    
    companion object {
        @Volatile
        private var INSTANCE: UpgradeTaskManager? = null
        
        fun getInstance(): UpgradeTaskManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UpgradeTaskManager().also { INSTANCE = it }
            }
        }
        
        // 目标共享目录
        const val SHARE_PATH = "/ota_share/"
    }
    
    /**
     * 升级任务信息
     */
    data class UpgradeTaskInfo(
        val taskId: String,
        val upgradeTask: UpgradeTask,
        val usbPath: String,
        val status: TaskStatus,
        val createTime: Long = System.currentTimeMillis()
    )
    
    /**
     * 任务状态枚举
     */
    enum class TaskStatus {
        PENDING,        // 待处理
        COPYING,        // 拷贝中
        READY,          // 准备就绪
        PUBLISHED,      // 已发布
        COMPLETED,      // 已完成
        FAILED,         // 失败
        CANCELLED       // 已取消
    }
    
    /**
     * 设置S2S任务发布者
     */
    fun setS2STaskPublisher(publisher: S2STaskPublisher) {
        this.s2sTaskPublisher = publisher
    }
    
    /**
     * 处理升级任务
     */
    fun processUpgradeTask(upgradeTask: UpgradeTask, usbDir: File) {
        val taskId = generateTaskId()
        val taskInfo = UpgradeTaskInfo(
            taskId = taskId,
            upgradeTask = upgradeTask,
            usbPath = usbDir.absolutePath,
            status = TaskStatus.PENDING
        )
        
        activeTasks[taskId] = taskInfo
        LogUtils.i(TAG, "开始处理升级任务: $taskId")
        
        threadPool.submit {
            try {
                // 1. 更新任务状态为拷贝中
                updateTaskStatus(taskId, TaskStatus.COPYING)
                
                // 2. 拷贝文件到目标目录
                val success = copyUpgradeFiles(usbDir, upgradeTask)
                
                if (success) {
                    // 3. 生成升级任务JSON
                    val taskJson = generateUpgradeTaskJson(upgradeTask)
                    
                    // 4. 更新任务状态为准备就绪
                    updateTaskStatus(taskId, TaskStatus.READY)
                    
                    // 5. 通过S2S发布任务
                    publishTaskToS2S(taskId, taskJson)
                    
                    LogUtils.i(TAG, "升级任务处理成功: $taskId")
                } else {
                    updateTaskStatus(taskId, TaskStatus.FAILED)
                    LogUtils.e(TAG, "升级任务文件拷贝失败: $taskId")
                }
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "处理升级任务失败 $taskId: ${e.message}")
                updateTaskStatus(taskId, TaskStatus.FAILED)
            }
        }
    }
    
    /**
     * 拷贝升级文件到目标目录
     */
    private fun copyUpgradeFiles(usbDir: File, upgradeTask: UpgradeTask): Boolean {
        return try {
            // 确保目标目录存在
            val shareDir = File(SHARE_PATH)
            if (!shareDir.exists()) {
                shareDir.mkdirs()
            }
            
            // 拷贝所有升级包文件
            for (packageInfo in upgradeTask.packageInfo) {
                val sourceFileName = packageInfo.packagePath.substringAfterLast("/")
                val sourceFile = findFileInDirectory(usbDir, sourceFileName)
                
                if (sourceFile != null && sourceFile.exists()) {
                    val targetFile = File(shareDir, sourceFileName)
                    copyFile(sourceFile, targetFile)
                    LogUtils.d(TAG, "文件拷贝成功: ${sourceFile.name} -> ${targetFile.absolutePath}")
                } else {
                    LogUtils.e(TAG, "源文件不存在: $sourceFileName")
                    return false
                }
            }
            
            true
        } catch (e: Exception) {
            LogUtils.e(TAG, "拷贝升级文件失败: ${e.message}")
            false
        }
    }
    
    /**
     * 在目录中查找文件
     */
    private fun findFileInDirectory(dir: File, fileName: String): File? {
        dir.listFiles()?.forEach { file ->
            if (file.isFile && file.name == fileName) {
                return file
            } else if (file.isDirectory) {
                val found = findFileInDirectory(file, fileName)
                if (found != null) return found
            }
        }
        return null
    }
    
    /**
     * 拷贝文件
     */
    private fun copyFile(source: File, target: File) {
        FileInputStream(source).use { input ->
            FileOutputStream(target).use { output ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    output.write(buffer, 0, bytesRead)
                }
                output.flush()
            }
        }
    }
    
    /**
     * 生成升级任务JSON
     */
    private fun generateUpgradeTaskJson(upgradeTask: UpgradeTask): String {
        return gson.toJson(upgradeTask)
    }
    
    /**
     * 通过S2S发布任务
     */
    private fun publishTaskToS2S(taskId: String, taskJson: String) {
        s2sTaskPublisher?.publishUpgradeTask(taskId, taskJson) { success ->
            if (success) {
                updateTaskStatus(taskId, TaskStatus.PUBLISHED)
                LogUtils.i(TAG, "升级任务发布成功: $taskId")
            } else {
                updateTaskStatus(taskId, TaskStatus.FAILED)
                LogUtils.e(TAG, "升级任务发布失败: $taskId")
            }
        }
    }
    
    /**
     * 更新任务状态
     */
    private fun updateTaskStatus(taskId: String, status: TaskStatus) {
        activeTasks[taskId]?.let { taskInfo ->
            val updatedTaskInfo = taskInfo.copy(status = status)
            activeTasks[taskId] = updatedTaskInfo
            LogUtils.d(TAG, "任务状态更新: $taskId -> $status")
        }
    }
    
    /**
     * 根据USB路径取消任务
     */
    fun cancelTasksByUsbPath(usbPath: String) {
        LogUtils.i(TAG, "取消USB路径相关的任务: $usbPath")
        
        val tasksToCancel = activeTasks.values.filter { it.usbPath == usbPath }
        
        for (taskInfo in tasksToCancel) {
            updateTaskStatus(taskInfo.taskId, TaskStatus.CANCELLED)
            activeTasks.remove(taskInfo.taskId)
            LogUtils.i(TAG, "已取消任务: ${taskInfo.taskId}")
        }
    }
    
    /**
     * 生成任务ID
     */
    private fun generateTaskId(): String {
        return "UPGRADE_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    /**
     * 获取活跃任务列表
     */
    fun getActiveTasks(): List<UpgradeTaskInfo> {
        return activeTasks.values.toList()
    }
    
    /**
     * 获取指定任务
     */
    fun getTask(taskId: String): UpgradeTaskInfo? {
        return activeTasks[taskId]
    }
}
