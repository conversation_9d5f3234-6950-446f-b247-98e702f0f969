package com.seres.background.upgrade.monitor

import android.content.Context
import com.google.gson.Gson
import com.seres.background.upgrade.utils.LogUtils
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 状态监控器
 * 用于记录和监控服务运行状态，便于调试和验证
 */
class StatusMonitor(private val context: Context) {
    
    private val TAG = "StatusMonitor"
    private val gson = Gson()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    // 状态文件路径
    private val statusDir = File(context.getExternalFilesDir(null), "status")
    private val serviceStatusFile = File(statusDir, "service_status.json")
    private val usbStatusFile = File(statusDir, "usb_status.json")
    private val taskStatusFile = File(statusDir, "task_status.json")
    private val heartbeatFile = File(statusDir, "heartbeat.txt")
    
    init {
        // 确保状态目录存在
        if (!statusDir.exists()) {
            statusDir.mkdirs()
        }
        LogUtils.i(TAG, "状态监控器初始化完成，状态目录: ${statusDir.absolutePath}")
    }
    
    /**
     * 服务状态数据类
     */
    data class ServiceStatus(
        val serviceName: String,
        val isRunning: Boolean,
        val startTime: String,
        val lastUpdateTime: String,
        val pid: Int = android.os.Process.myPid()
    )
    
    /**
     * USB状态数据类
     */
    data class UsbStatus(
        val action: String,
        val devicePath: String,
        val timestamp: String,
        val hasDeviceList: Boolean = false,
        val deviceCount: Int = 0,
        val packageCount: Int = 0
    )
    
    /**
     * 任务状态数据类
     */
    data class TaskStatus(
        val taskId: String,
        val status: String,
        val createTime: String,
        val updateTime: String,
        val usbPath: String,
        val packageCount: Int,
        val errorMessage: String = ""
    )
    
    /**
     * 记录服务状态
     */
    fun recordServiceStatus(serviceName: String, isRunning: Boolean, startTime: String = "") {
        try {
            val status = ServiceStatus(
                serviceName = serviceName,
                isRunning = isRunning,
                startTime = startTime.ifEmpty { dateFormat.format(Date()) },
                lastUpdateTime = dateFormat.format(Date())
            )
            
            val statusJson = gson.toJson(status)
            FileWriter(serviceStatusFile).use { writer ->
                writer.write(statusJson)
                writer.flush()
            }
            
            LogUtils.i(TAG, "服务状态已记录: $serviceName -> $isRunning")
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "记录服务状态失败: ${e.message}")
        }
    }
    
    /**
     * 记录USB状态
     */
    fun recordUsbStatus(action: String, devicePath: String, hasDeviceList: Boolean = false, 
                       deviceCount: Int = 0, packageCount: Int = 0) {
        try {
            val status = UsbStatus(
                action = action,
                devicePath = devicePath,
                timestamp = dateFormat.format(Date()),
                hasDeviceList = hasDeviceList,
                deviceCount = deviceCount,
                packageCount = packageCount
            )
            
            val statusJson = gson.toJson(status)
            FileWriter(usbStatusFile).use { writer ->
                writer.write(statusJson)
                writer.flush()
            }
            
            LogUtils.i(TAG, "USB状态已记录: $action -> $devicePath")
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "记录USB状态失败: ${e.message}")
        }
    }
    
    /**
     * 记录任务状态
     */
    fun recordTaskStatus(taskId: String, status: String, usbPath: String, 
                        packageCount: Int, errorMessage: String = "") {
        try {
            val taskStatus = TaskStatus(
                taskId = taskId,
                status = status,
                createTime = dateFormat.format(Date()),
                updateTime = dateFormat.format(Date()),
                usbPath = usbPath,
                packageCount = packageCount,
                errorMessage = errorMessage
            )
            
            val statusJson = gson.toJson(taskStatus)
            FileWriter(taskStatusFile).use { writer ->
                writer.write(statusJson)
                writer.flush()
            }
            
            LogUtils.i(TAG, "任务状态已记录: $taskId -> $status")
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "记录任务状态失败: ${e.message}")
        }
    }
    
    /**
     * 更新心跳
     */
    fun updateHeartbeat() {
        try {
            val heartbeat = "ALIVE:${dateFormat.format(Date())}:PID:${android.os.Process.myPid()}"
            FileWriter(heartbeatFile).use { writer ->
                writer.write(heartbeat)
                writer.flush()
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "更新心跳失败: ${e.message}")
        }
    }
    
    /**
     * 获取状态文件路径信息
     */
    fun getStatusFilePaths(): Map<String, String> {
        return mapOf(
            "statusDir" to statusDir.absolutePath,
            "serviceStatus" to serviceStatusFile.absolutePath,
            "usbStatus" to usbStatusFile.absolutePath,
            "taskStatus" to taskStatusFile.absolutePath,
            "heartbeat" to heartbeatFile.absolutePath
        )
    }
    
    /**
     * 读取服务状态
     */
    fun readServiceStatus(): ServiceStatus? {
        return try {
            if (serviceStatusFile.exists()) {
                val statusJson = serviceStatusFile.readText()
                gson.fromJson(statusJson, ServiceStatus::class.java)
            } else null
        } catch (e: Exception) {
            LogUtils.e(TAG, "读取服务状态失败: ${e.message}")
            null
        }
    }
    
    /**
     * 读取USB状态
     */
    fun readUsbStatus(): UsbStatus? {
        return try {
            if (usbStatusFile.exists()) {
                val statusJson = usbStatusFile.readText()
                gson.fromJson(statusJson, UsbStatus::class.java)
            } else null
        } catch (e: Exception) {
            LogUtils.e(TAG, "读取USB状态失败: ${e.message}")
            null
        }
    }
    
    /**
     * 读取任务状态
     */
    fun readTaskStatus(): TaskStatus? {
        return try {
            if (taskStatusFile.exists()) {
                val statusJson = taskStatusFile.readText()
                gson.fromJson(statusJson, TaskStatus::class.java)
            } else null
        } catch (e: Exception) {
            LogUtils.e(TAG, "读取任务状态失败: ${e.message}")
            null
        }
    }
    
    /**
     * 检查心跳
     */
    fun checkHeartbeat(): String? {
        return try {
            if (heartbeatFile.exists()) {
                heartbeatFile.readText().trim()
            } else null
        } catch (e: Exception) {
            LogUtils.e(TAG, "检查心跳失败: ${e.message}")
            null
        }
    }
    
    /**
     * 清理旧状态文件
     */
    fun cleanup() {
        try {
            listOf(serviceStatusFile, usbStatusFile, taskStatusFile, heartbeatFile).forEach { file ->
                if (file.exists()) {
                    file.delete()
                }
            }
            LogUtils.i(TAG, "状态文件清理完成")
        } catch (e: Exception) {
            LogUtils.e(TAG, "清理状态文件失败: ${e.message}")
        }
    }
}
