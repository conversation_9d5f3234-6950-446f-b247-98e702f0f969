package com.seres.background.upgrade.utils

import android.content.Context
import com.google.gson.Gson
import com.seres.background.upgrade.model.*
import com.seres.background.upgrade.monitor.StatusMonitor
import java.io.File

/**
 * 测试工具类
 * 用于生成测试数据和验证功能
 */
object TestUtils {
    
    private val gson = Gson()
    
    /**
     * 生成示例device_list.json内容（新格式）
     */
    fun generateSampleDeviceListJson(): String {
        val deviceListConfig = DeviceListConfig(
            deviceList = DomainDevices(
                cdcDomain = listOf(
                    EcuDevice(
                        ecuName = "device1",
                        ipAddr = "0.0.0.0",
                        phyAddr = "0x1234"
                    ),
                    EcuDevice(
                        ecuName = "device2",
                        ipAddr = "0.0.0.0",
                        phyAddr = "0x1235"
                    )
                ),
                mdcDomain = listOf(
                    EcuDevice(
                        ecuName = "device3",
                        ipAddr = "0.0.0.0",
                        phyAddr = "0x1236"
                    ),
                    EcuDevice(
                        ecuName = "device4",
                        ipAddr = "0.0.0.0",
                        phyAddr = "0x1237"
                    )
                ),
                vdcDomain = listOf(
                    EcuDevice(
                        ecuName = "device5",
                        ipAddr = "0.0.0.0",
                        phyAddr = "0x1238"
                    ),
                    EcuDevice(
                        ecuName = "device6",
                        ipAddr = "0.0.0.0",
                        phyAddr = "0x1239"
                    )
                )
            )
        )

        return gson.toJson(deviceListConfig)
    }
    
    /**
     * 生成示例升级任务JSON
     */
    fun generateSampleUpgradeTaskJson(): String {
        val upgradeTask = UpgradeTask(
            version = "1.0",
            sharePath = "/ota_share/",
            packageInfo = listOf(
                PackageInfo(
                    ecuName = "HPCM",
                    seamless = true,
                    packagePath = "/ota_share/HPCM_v1.1.0.zip",
                    packageSize = 1024000,
                    packageMd5 = "d41d8cd98f00b204e9800998ecf8427e"
                ),
                PackageInfo(
                    ecuName = "ZCUF",
                    seamless = false,
                    packagePath = "/ota_share/ZCUF_v2.1.0.zip",
                    packageSize = 2048000,
                    packageMd5 = "098f6bcd4621d373cade4e832627b4f6"
                )
            ),
            preconditions = Preconditions(
                powerBatteryThreshold = 15,
                batteryThreshold = 65
            ),
            preferences = mapOf(
                "autoReboot" to true,
                "backupBeforeUpgrade" to true
            )
        )
        
        return gson.toJson(upgradeTask)
    }
    
    /**
     * 生成示例资产信息
     */
    fun generateSampleInventoryInfo(): List<InventoryInfo> {
        return listOf(
            InventoryInfo(
                partNumber = "PN001",
                softwareVersion = "1.0.0",
                supplierCode = "SUP001",
                ecuName = "HPCM",
                serialNumber = "SN001",
                hardwareVersion = "HW1.0",
                bootloaderVersion = "BL1.0",
                backupVersion = "BK1.0"
            ),
            InventoryInfo(
                partNumber = "PN002",
                softwareVersion = "2.0.0",
                supplierCode = "SUP002",
                ecuName = "ZCUF",
                serialNumber = "SN002",
                hardwareVersion = "HW2.0",
                bootloaderVersion = "BL2.0",
                backupVersion = "BK2.0"
            ),
            InventoryInfo(
                partNumber = "PN003",
                softwareVersion = "1.5.0",
                supplierCode = "SUP003",
                ecuName = "BCM",
                serialNumber = "SN003",
                hardwareVersion = "HW1.5",
                bootloaderVersion = "BL1.5",
                backupVersion = "BK1.5"
            )
        )
    }
    
    /**
     * 验证device_list.json格式（新格式）
     */
    fun validateDeviceListJson(jsonString: String): Boolean {
        return try {
            // 尝试解析新格式
            val config = gson.fromJson(jsonString, DeviceListConfig::class.java)
            val hasDevices = config.deviceList.cdcDomain.isNotEmpty() ||
                           config.deviceList.mdcDomain.isNotEmpty() ||
                           config.deviceList.vdcDomain.isNotEmpty()

            if (hasDevices) {
                return true
            }

            // 尝试解析旧格式（兼容性）
            val legacyConfig = gson.fromJson(jsonString, LegacyDeviceListConfig::class.java)
            legacyConfig.devices.isNotEmpty() && legacyConfig.version.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 验证升级任务JSON格式
     */
    fun validateUpgradeTaskJson(jsonString: String): Boolean {
        return try {
            val task = gson.fromJson(jsonString, UpgradeTask::class.java)
            task.packageInfo.isNotEmpty() && task.sharePath.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 打印格式化的JSON
     */
    fun printFormattedJson(jsonString: String): String {
        return try {
            val jsonObject = gson.fromJson(jsonString, Any::class.java)
            gson.newBuilder().setPrettyPrinting().create().toJson(jsonObject)
        } catch (e: Exception) {
            jsonString
        }
    }

    /**
     * 创建测试用的device_list.json文件
     */
    fun createTestDeviceListFile(targetDir: File): Boolean {
        return try {
            val deviceListFile = File(targetDir, "device_list.json")
            val deviceListJson = generateSampleDeviceListJson()
            deviceListFile.writeText(deviceListJson)
            LogUtils.i("TestUtils", "测试device_list.json文件已创建: ${deviceListFile.absolutePath}")
            true
        } catch (e: Exception) {
            LogUtils.e("TestUtils", "创建测试device_list.json文件失败: ${e.message}")
            false
        }
    }

    /**
     * 创建测试用的升级包文件（新格式对应）
     */
    fun createTestUpgradePackages(targetDir: File): Boolean {
        return try {
            // 为新格式的设备创建对应的升级包文件
            val packageFiles = listOf(
                "device1.zip", "device1_upgrade.bin",
                "device2.zip", "device2_firmware.hex",
                "device3.zip", "device3_update.img",
                "device4.zip", "device4_package.tar",
                "device5.zip", "device5_software.bin",
                "device6.zip", "device6_firmware.zip"
            )

            for (fileName in packageFiles) {
                val packageFile = File(targetDir, fileName)
                // 创建一个简单的测试文件内容
                val testContent = "Test upgrade package: $fileName\nCreated at: ${System.currentTimeMillis()}\nDevice: ${fileName.substringBefore(".")}"
                packageFile.writeText(testContent)
                LogUtils.i("TestUtils", "测试升级包文件已创建: ${packageFile.absolutePath}")
            }

            true
        } catch (e: Exception) {
            LogUtils.e("TestUtils", "创建测试升级包文件失败: ${e.message}")
            false
        }
    }

    /**
     * 获取调试信息
     */
    fun getDebugInfo(context: Context): String {
        val statusMonitor = StatusMonitor(context)
        val debugInfo = StringBuilder()

        debugInfo.append("=== 后台升级服务调试信息 ===\n")
        debugInfo.append("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}\n")
        debugInfo.append("PID: ${android.os.Process.myPid()}\n\n")

        // 状态文件路径
        debugInfo.append("=== 状态文件路径 ===\n")
        val statusPaths = statusMonitor.getStatusFilePaths()
        statusPaths.forEach { (key, path) ->
            debugInfo.append("$key: $path\n")
        }
        debugInfo.append("\n")

        // 服务状态
        debugInfo.append("=== 服务状态 ===\n")
        val serviceStatus = statusMonitor.readServiceStatus()
        if (serviceStatus != null) {
            debugInfo.append("服务名: ${serviceStatus.serviceName}\n")
            debugInfo.append("运行状态: ${serviceStatus.isRunning}\n")
            debugInfo.append("启动时间: ${serviceStatus.startTime}\n")
            debugInfo.append("最后更新: ${serviceStatus.lastUpdateTime}\n")
            debugInfo.append("PID: ${serviceStatus.pid}\n")
        } else {
            debugInfo.append("未找到服务状态信息\n")
        }
        debugInfo.append("\n")

        // USB状态
        debugInfo.append("=== USB状态 ===\n")
        val usbStatus = statusMonitor.readUsbStatus()
        if (usbStatus != null) {
            debugInfo.append("动作: ${usbStatus.action}\n")
            debugInfo.append("设备路径: ${usbStatus.devicePath}\n")
            debugInfo.append("时间戳: ${usbStatus.timestamp}\n")
            debugInfo.append("包含device_list: ${usbStatus.hasDeviceList}\n")
            debugInfo.append("设备数量: ${usbStatus.deviceCount}\n")
            debugInfo.append("升级包数量: ${usbStatus.packageCount}\n")
        } else {
            debugInfo.append("未找到USB状态信息\n")
        }
        debugInfo.append("\n")

        // 任务状态
        debugInfo.append("=== 任务状态 ===\n")
        val taskStatus = statusMonitor.readTaskStatus()
        if (taskStatus != null) {
            debugInfo.append("任务ID: ${taskStatus.taskId}\n")
            debugInfo.append("状态: ${taskStatus.status}\n")
            debugInfo.append("创建时间: ${taskStatus.createTime}\n")
            debugInfo.append("更新时间: ${taskStatus.updateTime}\n")
            debugInfo.append("USB路径: ${taskStatus.usbPath}\n")
            debugInfo.append("升级包数量: ${taskStatus.packageCount}\n")
            if (taskStatus.errorMessage.isNotEmpty()) {
                debugInfo.append("错误信息: ${taskStatus.errorMessage}\n")
            }
        } else {
            debugInfo.append("未找到任务状态信息\n")
        }
        debugInfo.append("\n")

        // 心跳检查
        debugInfo.append("=== 心跳检查 ===\n")
        val heartbeat = statusMonitor.checkHeartbeat()
        if (heartbeat != null) {
            debugInfo.append("心跳: $heartbeat\n")
        } else {
            debugInfo.append("未找到心跳信息\n")
        }
        debugInfo.append("\n")

        // 升级任务JSON
        debugInfo.append("=== 升级任务JSON ===\n")
        val latestTaskJson = statusMonitor.readLatestUpgradeTaskJson()
        if (latestTaskJson != null) {
            debugInfo.append("最新升级任务JSON:\n")
            debugInfo.append(printFormattedJson(latestTaskJson))
            debugInfo.append("\n")
        } else {
            debugInfo.append("未找到升级任务JSON\n")
        }

        // 升级任务文件列表
        val taskJsonFiles = statusMonitor.getUpgradeTaskJsonFiles()
        if (taskJsonFiles.isNotEmpty()) {
            debugInfo.append("历史升级任务文件:\n")
            taskJsonFiles.forEach { fileName ->
                debugInfo.append("  - $fileName\n")
            }
        } else {
            debugInfo.append("无历史升级任务文件\n")
        }

        return debugInfo.toString()
    }
}
