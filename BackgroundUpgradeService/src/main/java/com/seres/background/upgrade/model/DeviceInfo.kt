package com.seres.background.upgrade.model

import com.google.gson.annotations.SerializedName

/**
 * ECU设备信息数据类
 */
data class EcuDevice(
    @SerializedName("ecu_name")
    val ecuName: String = "",
    @SerializedName("ip_addr")
    val ipAddr: String = "0.0.0.0",
    @SerializedName("phy_addr")
    val phyAddr: String = ""
)

/**
 * 域设备列表数据类
 */
data class DomainDevices(
    @SerializedName("cdc_domain")
    val cdcDomain: List<EcuDevice> = emptyList(),
    @SerializedName("mdc_domain")
    val mdcDomain: List<EcuDevice> = emptyList(),
    @SerializedName("vdc_domain")
    val vdcDomain: List<EcuDevice> = emptyList()
)

/**
 * 设备列表配置数据类（新格式）
 */
data class DeviceListConfig(
    @SerializedName("device_list")
    val deviceList: DomainDevices = DomainDevices()
)

/**
 * 旧版设备信息数据类（保持兼容性）
 */
data class LegacyDeviceInfo(
    val deviceId: String = "",
    val deviceName: String = "",
    val currentVersion: String = "",
    val targetVersion: String = "",
    val seamless: Boolean = true,
    val packageFileName: String = ""
)

/**
 * 旧版设备列表配置数据类（保持兼容性）
 */
data class LegacyDeviceListConfig(
    val version: String = "1.0",
    val devices: List<LegacyDeviceInfo> = emptyList(),
    val preconditions: Preconditions = Preconditions(),
    val preferences: Map<String, Any> = emptyMap()
)

/**
 * 前置条件数据类
 */
data class Preconditions(
    val powerBatteryThreshold: Int = 15,
    val batteryThreshold: Int = 65
)

/**
 * 升级包信息数据类
 */
data class PackageInfo(
    val ecuName: String = "",
    val seamless: Boolean = true,
    val packagePath: String = "",
    val packageSize: Long = 0L,
    val packageMd5: String = ""
)

/**
 * 升级任务数据类
 */
data class UpgradeTask(
    val version: String = "1.0",
    val sharePath: String = "/ota_share/",
    val packageInfo: List<PackageInfo> = emptyList(),
    val preconditions: Preconditions = Preconditions(),
    val preferences: Map<String, Any> = emptyMap()
)

/**
 * 资产信息数据类
 */
data class InventoryInfo(
    val partNumber: String = "",
    val softwareVersion: String = "",
    val supplierCode: String = "",
    val ecuName: String = "",
    val serialNumber: String = "",
    val hardwareVersion: String = "",
    val bootloaderVersion: String = "",
    val backupVersion: String = ""
)
