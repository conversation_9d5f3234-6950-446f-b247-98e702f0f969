package com.seres.background.upgrade.model

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val deviceId: String = "",
    val deviceName: String = "",
    val currentVersion: String = "",
    val targetVersion: String = "",
    val seamless: Boolean = true,
    val packageFileName: String = ""
)

/**
 * 设备列表配置数据类
 */
data class DeviceListConfig(
    val version: String = "1.0",
    val devices: List<DeviceInfo> = emptyList(),
    val preconditions: Preconditions = Preconditions(),
    val preferences: Map<String, Any> = emptyMap()
)

/**
 * 前置条件数据类
 */
data class Preconditions(
    val powerBatteryThreshold: Int = 15,
    val batteryThreshold: Int = 65
)

/**
 * 升级包信息数据类
 */
data class PackageInfo(
    val ecuName: String = "",
    val seamless: Boolean = true,
    val packagePath: String = "",
    val packageSize: Long = 0L,
    val packageMd5: String = ""
)

/**
 * 升级任务数据类
 */
data class UpgradeTask(
    val version: String = "1.0",
    val sharePath: String = "/ota_share/",
    val packageInfo: List<PackageInfo> = emptyList(),
    val preconditions: Preconditions = Preconditions(),
    val preferences: Map<String, Any> = emptyMap()
)

/**
 * 资产信息数据类
 */
data class InventoryInfo(
    val partNumber: String = "",
    val softwareVersion: String = "",
    val supplierCode: String = "",
    val ecuName: String = "",
    val serialNumber: String = "",
    val hardwareVersion: String = "",
    val bootloaderVersion: String = "",
    val backupVersion: String = ""
)
