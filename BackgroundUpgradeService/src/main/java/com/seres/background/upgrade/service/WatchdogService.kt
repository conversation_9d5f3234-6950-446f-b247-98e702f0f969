package com.seres.background.upgrade.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.seres.background.upgrade.utils.LogUtils
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * 守护服务
 * 监控主服务状态，确保主服务始终运行
 */
class WatchdogService : Service() {
    
    private val TAG = "WatchdogService"
    private var watchdogExecutor: ScheduledExecutorService? = null
    
    companion object {
        const val CHECK_INTERVAL = 30L // 30秒检查一次
    }
    
    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "WatchdogService onCreate")
        startWatchdog()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.i(TAG, "WatchdogService onStartCommand")
        
        if (watchdogExecutor?.isShutdown != false) {
            startWatchdog()
        }
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        LogUtils.i(TAG, "WatchdogService onDestroy")
        stopWatchdog()
    }
    
    /**
     * 启动守护监控
     */
    private fun startWatchdog() {
        try {
            watchdogExecutor = Executors.newSingleThreadScheduledExecutor()
            watchdogExecutor?.scheduleAtFixedRate({
                checkAndRestartMainService()
            }, CHECK_INTERVAL, CHECK_INTERVAL, TimeUnit.SECONDS)
            
            LogUtils.i(TAG, "守护监控已启动，检查间隔: ${CHECK_INTERVAL}秒")
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动守护监控失败: ${e.message}")
        }
    }
    
    /**
     * 停止守护监控
     */
    private fun stopWatchdog() {
        try {
            watchdogExecutor?.shutdown()
            LogUtils.i(TAG, "守护监控已停止")
        } catch (e: Exception) {
            LogUtils.e(TAG, "停止守护监控失败: ${e.message}")
        }
    }
    
    /**
     * 检查并重启主服务
     */
    private fun checkAndRestartMainService() {
        try {
            // 检查主服务是否运行
            val isMainServiceRunning = isServiceRunning("com.seres.background.upgrade.service.BackgroundUpgradeService")
            val isUsbServiceRunning = isServiceRunning("com.seres.background.upgrade.service.UsbDetectionService")
            
            LogUtils.d(TAG, "服务状态检查 - 主服务: $isMainServiceRunning, USB服务: $isUsbServiceRunning")
            
            // 如果主服务未运行，尝试重启
            if (!isMainServiceRunning) {
                LogUtils.w(TAG, "检测到主服务未运行，尝试重启")
                restartMainService()
            }
            
            // 如果USB服务未运行，尝试重启
            if (!isUsbServiceRunning) {
                LogUtils.w(TAG, "检测到USB服务未运行，尝试重启")
                restartUsbService()
            }
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "检查服务状态失败: ${e.message}")
        }
    }
    
    /**
     * 检查服务是否运行
     */
    private fun isServiceRunning(serviceName: String): Boolean {
        return try {
            val activityManager = getSystemService(ACTIVITY_SERVICE) as android.app.ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            services.any { serviceInfo ->
                serviceInfo.service.className == serviceName
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "检查服务运行状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 重启主服务
     */
    private fun restartMainService() {
        try {
            val intent = Intent(this, BackgroundUpgradeService::class.java)
            intent.action = "com.seres.background.upgrade.WATCHDOG_RESTART"
            startForegroundService(intent)
            LogUtils.i(TAG, "已尝试重启主服务")
        } catch (e: Exception) {
            LogUtils.e(TAG, "重启主服务失败: ${e.message}")
        }
    }
    
    /**
     * 重启USB服务
     */
    private fun restartUsbService() {
        try {
            val intent = Intent(this, UsbDetectionService::class.java)
            intent.action = "com.seres.background.upgrade.WATCHDOG_RESTART"
            startForegroundService(intent)
            LogUtils.i(TAG, "已尝试重启USB服务")
        } catch (e: Exception) {
            LogUtils.e(TAG, "重启USB服务失败: ${e.message}")
        }
    }
}
