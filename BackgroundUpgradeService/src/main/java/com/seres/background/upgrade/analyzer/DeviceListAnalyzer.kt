package com.seres.background.upgrade.analyzer

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.seres.background.upgrade.model.*
import com.seres.background.upgrade.manager.UpgradeTaskManager
import com.seres.background.upgrade.monitor.StatusMonitor
import com.seres.background.upgrade.utils.LogUtils
import java.io.File
import java.io.FileInputStream
import java.security.MessageDigest

/**
 * 设备列表分析器
 * 分析device_list.json文件并生成升级任务
 */
class DeviceListAnalyzer {

    private val TAG = "DeviceListAnalyzer"
    private val gson = Gson()
    private var upgradeTaskManager: UpgradeTaskManager? = null
    private var statusMonitor: StatusMonitor? = null

    /**
     * 设置状态监控器
     */
    fun setStatusMonitor(monitor: StatusMonitor) {
        this.statusMonitor = monitor
    }
    
    // 支持的升级包文件扩展名
    private val supportedExtensions = setOf("zip", "bin", "hex", "img", "tar", "apk", "so")
    
    /**
     * 分析升级任务
     */
    fun analyzeUpgradeTask(usbDir: File) {
        LogUtils.i(TAG, "开始分析升级任务: ${usbDir.absolutePath}")
        
        try {
            // 1. 解析device_list.json文件
            val deviceListFile = File(usbDir, "device_list.json")
            val deviceListConfig = parseDeviceListConfig(deviceListFile)
            
            if (deviceListConfig == null) {
                LogUtils.e(TAG, "解析device_list.json失败")
                return
            }
            
            // 2. 扫描升级包文件
            val packageInfoList = scanUpgradePackages(usbDir, deviceListConfig)
            
            if (packageInfoList.isEmpty()) {
                LogUtils.w(TAG, "未找到匹配的升级包文件")
                return
            }
            
            // 3. 生成升级任务
            val upgradeTask = UpgradeTask(
                version = deviceListConfig.version,
                sharePath = "/ota_share/",
                packageInfo = packageInfoList,
                preconditions = deviceListConfig.preconditions,
                preferences = deviceListConfig.preferences
            )
            
            // 4. 记录分析结果
            statusMonitor?.recordUsbStatus(
                "ANALYSIS_COMPLETE",
                usbDir.absolutePath,
                true,
                deviceListConfig.devices.size,
                packageInfoList.size
            )

            // 5. 处理升级任务
            getUpgradeTaskManager()?.processUpgradeTask(upgradeTask, usbDir)

            LogUtils.i(TAG, "升级任务分析完成，包含 ${packageInfoList.size} 个升级包")
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "分析升级任务失败: ${e.message}")
        }
    }
    
    /**
     * 解析device_list.json配置文件
     */
    private fun parseDeviceListConfig(configFile: File): DeviceListConfig? {
        return try {
            val configJson = configFile.readText()
            gson.fromJson(configJson, DeviceListConfig::class.java)
        } catch (e: JsonSyntaxException) {
            LogUtils.e(TAG, "device_list.json格式错误: ${e.message}")
            null
        } catch (e: Exception) {
            LogUtils.e(TAG, "读取device_list.json失败: ${e.message}")
            null
        }
    }
    
    /**
     * 扫描升级包文件
     */
    private fun scanUpgradePackages(usbDir: File, config: DeviceListConfig): List<PackageInfo> {
        val packageInfoList = mutableListOf<PackageInfo>()
        
        // 获取USB目录下的所有文件
        val allFiles = getAllFiles(usbDir)
        
        // 为每个设备查找对应的升级包
        for (device in config.devices) {
            val packageFile = findPackageFile(allFiles, device.packageFileName)
            
            if (packageFile != null) {
                val packageInfo = createPackageInfo(device, packageFile)
                packageInfoList.add(packageInfo)
                LogUtils.d(TAG, "找到升级包: ${device.deviceId} -> ${packageFile.name}")
            } else {
                LogUtils.w(TAG, "未找到设备 ${device.deviceId} 的升级包: ${device.packageFileName}")
            }
        }
        
        return packageInfoList
    }
    
    /**
     * 递归获取目录下的所有文件
     */
    private fun getAllFiles(dir: File): List<File> {
        val files = mutableListOf<File>()
        
        dir.listFiles()?.forEach { file ->
            if (file.isFile && isUpgradePackage(file)) {
                files.add(file)
            } else if (file.isDirectory) {
                files.addAll(getAllFiles(file))
            }
        }
        
        return files
    }
    
    /**
     * 判断是否为升级包文件
     */
    private fun isUpgradePackage(file: File): Boolean {
        val extension = file.extension.lowercase()
        return supportedExtensions.contains(extension)
    }
    
    /**
     * 查找指定名称的包文件
     */
    private fun findPackageFile(files: List<File>, fileName: String): File? {
        return files.find { it.name == fileName }
    }
    
    /**
     * 创建包信息
     */
    private fun createPackageInfo(device: DeviceInfo, packageFile: File): PackageInfo {
        val md5 = calculateMD5(packageFile)
        
        return PackageInfo(
            ecuName = device.deviceId,
            seamless = device.seamless,
            packagePath = "/ota_share/${packageFile.name}",
            packageSize = packageFile.length(),
            packageMd5 = md5
        )
    }
    
    /**
     * 计算文件MD5值
     */
    private fun calculateMD5(file: File): String {
        return try {
            val md5 = MessageDigest.getInstance("MD5")
            FileInputStream(file).use { fis ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (fis.read(buffer).also { bytesRead = it } != -1) {
                    md5.update(buffer, 0, bytesRead)
                }
            }
            md5.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            LogUtils.w(TAG, "计算MD5失败: ${file.name} - ${e.message}")
            ""
        }
    }
    
    /**
     * 获取升级任务管理器实例
     */
    private fun getUpgradeTaskManager(): UpgradeTaskManager? {
        if (upgradeTaskManager == null) {
            upgradeTaskManager = UpgradeTaskManager.getInstance()
        }
        return upgradeTaskManager
    }
    
    /**
     * 根据USB路径取消任务
     */
    fun cancelTasksByUsbPath(usbPath: String) {
        LogUtils.i(TAG, "取消USB路径相关的任务: $usbPath")
        upgradeTaskManager?.cancelTasksByUsbPath(usbPath)
    }
}
