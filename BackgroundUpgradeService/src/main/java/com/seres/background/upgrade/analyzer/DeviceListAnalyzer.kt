package com.seres.background.upgrade.analyzer

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.seres.background.upgrade.model.*
import com.seres.background.upgrade.manager.UpgradeTaskManager
import com.seres.background.upgrade.monitor.StatusMonitor
import com.seres.background.upgrade.utils.LogUtils
import java.io.File
import java.io.FileInputStream
import java.security.MessageDigest

/**
 * 设备列表分析器
 * 分析device_list.json文件并生成升级任务
 */
class DeviceListAnalyzer {

    private val TAG = "DeviceListAnalyzer"
    private val gson = Gson()
    private var upgradeTaskManager: UpgradeTaskManager? = null
    private var statusMonitor: StatusMonitor? = null

    /**
     * 设置状态监控器
     */
    fun setStatusMonitor(monitor: StatusMonitor) {
        this.statusMonitor = monitor
    }
    
    // 支持的升级包文件扩展名
    private val supportedExtensions = setOf("zip", "bin", "hex", "img", "tar", "apk", "so")
    
    /**
     * 分析升级任务
     */
    fun analyzeUpgradeTask(usbDir: File) {
        LogUtils.i(TAG, "开始分析升级任务: ${usbDir.absolutePath}")

        try {
            // 1. 解析device_list.json文件
            val deviceListFile = File(usbDir, "device_list.json")
            val deviceListConfig = parseDeviceListConfig(deviceListFile)

            if (deviceListConfig == null) {
                LogUtils.e(TAG, "解析device_list.json失败")
                return
            }

            // 2. 获取所有设备列表
            val allDevices = getAllDevicesFromConfig(deviceListConfig)

            if (allDevices.isEmpty()) {
                LogUtils.w(TAG, "device_list.json中未找到任何设备")
                return
            }

            LogUtils.i(TAG, "发现 ${allDevices.size} 个设备，开始扫描升级包")

            // 3. 扫描升级包文件
            val packageInfoList = scanUpgradePackagesForDevices(usbDir, allDevices)

            if (packageInfoList.isEmpty()) {
                LogUtils.w(TAG, "未找到匹配的升级包文件")
                // 即使没有升级包，也记录设备列表存在的状态
                statusMonitor?.recordUsbStatus(
                    "DEVICE_LIST_FOUND_NO_PACKAGES",
                    usbDir.absolutePath,
                    true,
                    allDevices.size,
                    0
                )
                return
            }

            // 4. 生成升级任务
            val upgradeTask = UpgradeTask(
                version = "1.0",
                sharePath = "/ota_share/",
                packageInfo = packageInfoList,
                preconditions = Preconditions(), // 使用默认前置条件
                preferences = emptyMap()
            )

            // 5. 记录分析结果
            statusMonitor?.recordUsbStatus(
                "ANALYSIS_COMPLETE",
                usbDir.absolutePath,
                true,
                allDevices.size,
                packageInfoList.size
            )

            // 6. 处理升级任务
            getUpgradeTaskManager()?.processUpgradeTask(upgradeTask, usbDir)

            LogUtils.i(TAG, "升级任务分析完成，包含 ${packageInfoList.size} 个升级包")

        } catch (e: Exception) {
            LogUtils.e(TAG, "分析升级任务失败: ${e.message}")
        }
    }
    
    /**
     * 解析device_list.json配置文件
     */
    private fun parseDeviceListConfig(configFile: File): DeviceListConfig? {
        return try {
            val configJson = configFile.readText()
            LogUtils.d(TAG, "读取到device_list.json内容: $configJson")

            // 尝试解析新格式
            try {
                val newConfig = gson.fromJson(configJson, DeviceListConfig::class.java)
                if (newConfig.deviceList.cdcDomain.isNotEmpty() ||
                    newConfig.deviceList.mdcDomain.isNotEmpty() ||
                    newConfig.deviceList.vdcDomain.isNotEmpty()) {
                    LogUtils.i(TAG, "检测到新格式的device_list.json")
                    return newConfig
                }
            } catch (e: Exception) {
                LogUtils.d(TAG, "新格式解析失败，尝试旧格式: ${e.message}")
            }

            // 尝试解析旧格式（保持兼容性）
            try {
                val legacyConfig = gson.fromJson(configJson, LegacyDeviceListConfig::class.java)
                if (legacyConfig.devices.isNotEmpty()) {
                    LogUtils.i(TAG, "检测到旧格式的device_list.json，转换为新格式")
                    return convertLegacyToNewFormat(legacyConfig)
                }
            } catch (e: Exception) {
                LogUtils.d(TAG, "旧格式解析也失败: ${e.message}")
            }

            LogUtils.e(TAG, "无法解析device_list.json，格式不正确")
            null

        } catch (e: JsonSyntaxException) {
            LogUtils.e(TAG, "device_list.json格式错误: ${e.message}")
            null
        } catch (e: Exception) {
            LogUtils.e(TAG, "读取device_list.json失败: ${e.message}")
            null
        }
    }

    /**
     * 将旧格式转换为新格式（保持兼容性）
     */
    private fun convertLegacyToNewFormat(legacyConfig: LegacyDeviceListConfig): DeviceListConfig {
        val cdcDevices = mutableListOf<EcuDevice>()

        // 将旧格式的设备转换为CDC域设备
        legacyConfig.devices.forEach { device ->
            cdcDevices.add(EcuDevice(
                ecuName = device.deviceId,
                ipAddr = "0.0.0.0",
                phyAddr = "0x1000" // 默认物理地址
            ))
        }

        return DeviceListConfig(
            deviceList = DomainDevices(
                cdcDomain = cdcDevices,
                mdcDomain = emptyList(),
                vdcDomain = emptyList()
            )
        )
    }
    
    /**
     * 从配置中获取所有设备列表
     */
    private fun getAllDevicesFromConfig(config: DeviceListConfig): List<EcuDevice> {
        val allDevices = mutableListOf<EcuDevice>()

        // 添加CDC域设备
        allDevices.addAll(config.deviceList.cdcDomain)
        LogUtils.d(TAG, "CDC域设备数量: ${config.deviceList.cdcDomain.size}")

        // 添加MDC域设备
        allDevices.addAll(config.deviceList.mdcDomain)
        LogUtils.d(TAG, "MDC域设备数量: ${config.deviceList.mdcDomain.size}")

        // 添加VDC域设备
        allDevices.addAll(config.deviceList.vdcDomain)
        LogUtils.d(TAG, "VDC域设备数量: ${config.deviceList.vdcDomain.size}")

        LogUtils.i(TAG, "总设备数量: ${allDevices.size}")
        allDevices.forEach { device ->
            LogUtils.d(TAG, "设备: ${device.ecuName}, IP: ${device.ipAddr}, 物理地址: ${device.phyAddr}")
        }

        return allDevices
    }

    /**
     * 为设备扫描升级包文件
     */
    private fun scanUpgradePackagesForDevices(usbDir: File, devices: List<EcuDevice>): List<PackageInfo> {
        val packageInfoList = mutableListOf<PackageInfo>()

        // 获取USB目录下的所有文件
        val allFiles = getAllFiles(usbDir)
        LogUtils.d(TAG, "USB目录下找到 ${allFiles.size} 个升级包文件")

        // 为每个设备查找对应的升级包
        for (device in devices) {
            val packageFile = findPackageFileForDevice(allFiles, device.ecuName)

            if (packageFile != null) {
                val packageInfo = createPackageInfoForDevice(device, packageFile)
                packageInfoList.add(packageInfo)
                LogUtils.d(TAG, "找到升级包: ${device.ecuName} -> ${packageFile.name}")
            } else {
                LogUtils.w(TAG, "未找到设备 ${device.ecuName} 的升级包")
            }
        }

        return packageInfoList
    }
    
    /**
     * 递归获取目录下的所有文件
     */
    private fun getAllFiles(dir: File): List<File> {
        val files = mutableListOf<File>()
        
        dir.listFiles()?.forEach { file ->
            if (file.isFile && isUpgradePackage(file)) {
                files.add(file)
            } else if (file.isDirectory) {
                files.addAll(getAllFiles(file))
            }
        }
        
        return files
    }
    
    /**
     * 判断是否为升级包文件
     */
    private fun isUpgradePackage(file: File): Boolean {
        val extension = file.extension.lowercase()
        return supportedExtensions.contains(extension)
    }
    
    /**
     * 为设备查找升级包文件
     */
    private fun findPackageFileForDevice(files: List<File>, ecuName: String): File? {
        // 查找文件名包含设备名称的升级包
        return files.find { file ->
            val fileName = file.name.lowercase()
            val deviceName = ecuName.lowercase()

            // 匹配规则：文件名包含设备名称
            fileName.contains(deviceName) ||
            fileName.startsWith(deviceName) ||
            fileName.contains("${deviceName}_") ||
            fileName.contains("${deviceName}.") ||
            fileName == "$deviceName.zip" ||
            fileName == "$deviceName.bin" ||
            fileName == "$deviceName.hex" ||
            fileName == "$deviceName.img"
        }
    }

    /**
     * 为设备创建包信息
     */
    private fun createPackageInfoForDevice(device: EcuDevice, packageFile: File): PackageInfo {
        val md5 = calculateMD5(packageFile)

        return PackageInfo(
            ecuName = device.ecuName,
            seamless = true, // 默认为无缝升级
            packagePath = "/ota_share/${packageFile.name}",
            packageSize = packageFile.length(),
            packageMd5 = md5
        )
    }
    
    /**
     * 计算文件MD5值
     */
    private fun calculateMD5(file: File): String {
        return try {
            val md5 = MessageDigest.getInstance("MD5")
            FileInputStream(file).use { fis ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (fis.read(buffer).also { bytesRead = it } != -1) {
                    md5.update(buffer, 0, bytesRead)
                }
            }
            md5.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            LogUtils.w(TAG, "计算MD5失败: ${file.name} - ${e.message}")
            ""
        }
    }
    
    /**
     * 获取升级任务管理器实例
     */
    private fun getUpgradeTaskManager(): UpgradeTaskManager? {
        if (upgradeTaskManager == null) {
            upgradeTaskManager = UpgradeTaskManager.getInstance()
        }
        return upgradeTaskManager
    }
    
    /**
     * 根据USB路径取消任务
     */
    fun cancelTasksByUsbPath(usbPath: String) {
        LogUtils.i(TAG, "取消USB路径相关的任务: $usbPath")
        upgradeTaskManager?.cancelTasksByUsbPath(usbPath)
    }
}
