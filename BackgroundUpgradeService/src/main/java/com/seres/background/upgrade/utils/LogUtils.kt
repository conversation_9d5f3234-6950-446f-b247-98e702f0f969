package com.seres.background.upgrade.utils

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日志工具类
 * 支持控制台输出和文件记录
 */
object LogUtils {
    
    private const val TAG_PREFIX = "BackgroundUpgrade-"
    private const val LOG_FILE_PREFIX = "background_upgrade_"
    private const val LOG_FILE_EXTENSION = ".log"
    private const val MAX_LOG_FILES = 7 // 保留7天的日志
    
    private var isInitialized = false
    private var logDir: File? = null
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val timeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    /**
     * 初始化日志工具
     */
    fun init(context: Context) {
        if (isInitialized) return
        
        try {
            // 创建日志目录
            logDir = File(context.getExternalFilesDir(null), "logs")
            logDir?.let { dir ->
                if (!dir.exists()) {
                    dir.mkdirs()
                }
                
                // 清理旧日志文件
                cleanOldLogFiles(dir)
                
                isInitialized = true
                d("LogUtils", "日志工具初始化成功，日志目录: ${dir.absolutePath}")
            }
        } catch (e: Exception) {
            Log.e(TAG_PREFIX + "LogUtils", "日志工具初始化失败", e)
        }
    }
    
    /**
     * Debug级别日志
     */
    fun d(tag: String, message: String) {
        val fullTag = TAG_PREFIX + tag
        Log.d(fullTag, message)
        writeToFile("D", tag, message)
    }
    
    /**
     * Info级别日志
     */
    fun i(tag: String, message: String) {
        val fullTag = TAG_PREFIX + tag
        Log.i(fullTag, message)
        writeToFile("I", tag, message)
    }
    
    /**
     * Warning级别日志
     */
    fun w(tag: String, message: String) {
        val fullTag = TAG_PREFIX + tag
        Log.w(fullTag, message)
        writeToFile("W", tag, message)
    }
    
    /**
     * Error级别日志
     */
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        val fullTag = TAG_PREFIX + tag
        if (throwable != null) {
            Log.e(fullTag, message, throwable)
            writeToFile("E", tag, "$message\n${Log.getStackTraceString(throwable)}")
        } else {
            Log.e(fullTag, message)
            writeToFile("E", tag, message)
        }
    }
    
    /**
     * 写入日志到文件
     */
    private fun writeToFile(level: String, tag: String, message: String) {
        if (!isInitialized || logDir == null) return
        
        try {
            val today = dateFormat.format(Date())
            val logFile = File(logDir, "$LOG_FILE_PREFIX$today$LOG_FILE_EXTENSION")
            
            val timestamp = timeFormat.format(Date())
            val logEntry = "$timestamp $level/$tag: $message\n"
            
            FileWriter(logFile, true).use { writer ->
                writer.write(logEntry)
                writer.flush()
            }
        } catch (e: IOException) {
            Log.e(TAG_PREFIX + "LogUtils", "写入日志文件失败", e)
        }
    }
    
    /**
     * 清理旧日志文件
     */
    private fun cleanOldLogFiles(logDir: File) {
        try {
            val files = logDir.listFiles { file ->
                file.name.startsWith(LOG_FILE_PREFIX) && file.name.endsWith(LOG_FILE_EXTENSION)
            } ?: return
            
            val cutoffTime = System.currentTimeMillis() - (MAX_LOG_FILES * 24 * 60 * 60 * 1000L)
            
            files.forEach { file ->
                if (file.lastModified() < cutoffTime) {
                    file.delete()
                    Log.d(TAG_PREFIX + "LogUtils", "删除旧日志文件: ${file.name}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG_PREFIX + "LogUtils", "清理旧日志文件失败", e)
        }
    }
}
