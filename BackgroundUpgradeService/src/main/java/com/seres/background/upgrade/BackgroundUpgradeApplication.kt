package com.seres.background.upgrade

import android.app.Application
import android.content.Intent
import com.seres.background.upgrade.service.BackgroundUpgradeService
import com.seres.background.upgrade.utils.LogUtils

/**
 * 后台升级服务应用程序类
 * 负责应用程序的初始化和全局配置
 */
class BackgroundUpgradeApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        
        // 初始化日志工具
        LogUtils.init(this)
        LogUtils.d(TAG, "BackgroundUpgradeApplication onCreate")
        
        // 启动后台升级服务
        startBackgroundUpgradeService()
    }
    
    /**
     * 启动后台升级服务
     */
    private fun startBackgroundUpgradeService() {
        try {
            val intent = Intent(this, BackgroundUpgradeService::class.java)
            startForegroundService(intent)
            LogUtils.d(TAG, "后台升级服务启动成功")
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动后台升级服务失败", e)
        }
    }
    
    companion object {
        private const val TAG = "BackgroundUpgradeApplication"
    }
}
