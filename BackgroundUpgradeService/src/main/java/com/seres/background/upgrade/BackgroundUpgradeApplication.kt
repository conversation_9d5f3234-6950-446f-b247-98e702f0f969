package com.seres.background.upgrade

import android.app.Application
import android.content.Intent
import com.seres.background.upgrade.service.BackgroundUpgradeService
import com.seres.background.upgrade.service.WatchdogService
import com.seres.background.upgrade.utils.LogUtils

/**
 * 后台升级服务应用程序类
 * 负责应用程序的初始化和全局配置
 */
class BackgroundUpgradeApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        
        // 初始化日志工具
        LogUtils.init(this)
        LogUtils.d(TAG, "BackgroundUpgradeApplication onCreate")
        
        // 启动后台升级服务
        startBackgroundUpgradeService()

        // 启动守护服务
        startWatchdogService()
    }
    
    /**
     * 启动后台升级服务
     */
    private fun startBackgroundUpgradeService() {
        try {
            val intent = Intent(this, BackgroundUpgradeService::class.java)
            intent.action = "com.seres.background.upgrade.START_SERVICE"

            // 尝试启动前台服务
            try {
                startForegroundService(intent)
                LogUtils.d(TAG, "后台升级服务启动成功 (前台服务)")
            } catch (e: Exception) {
                // 如果前台服务启动失败，尝试普通服务
                LogUtils.w(TAG, "前台服务启动失败，尝试普通服务: ${e.message}")
                startService(intent)
                LogUtils.d(TAG, "后台升级服务启动成功 (普通服务)")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动后台升级服务失败", e)
        }
    }

    /**
     * 启动守护服务
     */
    private fun startWatchdogService() {
        try {
            val intent = Intent(this, WatchdogService::class.java)
            intent.action = "com.seres.background.upgrade.WATCHDOG_SERVICE"

            // 尝试启动前台服务
            try {
                startForegroundService(intent)
                LogUtils.d(TAG, "守护服务启动成功 (前台服务)")
            } catch (e: Exception) {
                // 如果前台服务启动失败，尝试普通服务
                LogUtils.w(TAG, "守护前台服务启动失败，尝试普通服务: ${e.message}")
                startService(intent)
                LogUtils.d(TAG, "守护服务启动成功 (普通服务)")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "启动守护服务失败", e)
        }
    }

    companion object {
        private const val TAG = "BackgroundUpgradeApplication"
    }
}
