<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 权限声明 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />

    <!-- USB相关权限 -->
    <uses-permission android:name="android.permission.USB_PERMISSION" />
    <uses-feature android:name="android.hardware.usb.host" android:required="false" />

    <application
        android:name=".BackgroundUpgradeApplication"
        android:allowBackup="false"
        android:icon="@android:drawable/ic_menu_manage"
        android:label="@string/app_name"
        android:theme="@android:style/Theme.NoDisplay"
        android:requestLegacyExternalStorage="true"
        android:persistent="true"
        android:killAfterRestore="false"
        android:restoreAnyVersion="true"
        tools:targetApi="31">

        <!-- 后台升级服务 -->
        <service
            android:name=".service.BackgroundUpgradeService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync"
            android:stopWithTask="false">
            <intent-filter>
                <action android:name="com.seres.background.upgrade.START_SERVICE" />
            </intent-filter>
        </service>

        <!-- USB检测服务 -->
        <service
            android:name=".service.UsbDetectionService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync"
            android:stopWithTask="false">
            <intent-filter>
                <action android:name="com.seres.background.upgrade.USB_DETECTION" />
            </intent-filter>
        </service>

        <!-- 守护服务 -->
        <service
            android:name=".service.WatchdogService"
            android:enabled="true"
            android:exported="false"
            android:stopWithTask="false">
        </service>

        <!-- 开机启动接收器 -->
        <receiver
            android:name=".receiver.BootCompleteReceiver"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <!-- USB设备插拔接收器 -->
        <receiver
            android:name=".receiver.UsbDeviceReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
                <action android:name="android.hardware.usb.action.USB_DEVICE_DETACHED" />
                <action android:name="android.intent.action.MEDIA_MOUNTED" />
                <action android:name="android.intent.action.MEDIA_UNMOUNTED" />
                <action android:name="android.intent.action.MEDIA_EJECT" />
                <data android:scheme="file" />
            </intent-filter>
        </receiver>

        <!-- 调试Activity (隐藏) -->
        <activity
            android:name=".DebugActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Material.Light.Dialog"
            android:excludeFromRecents="true"
            android:noHistory="true">
        </activity>

    </application>

</manifest>