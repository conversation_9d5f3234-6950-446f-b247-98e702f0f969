# 后台升级服务调试命令

## 1. 检查服务是否运行

### 检查应用是否安装
```bash
adb shell pm list packages | grep com.seres.background.upgrade
```

### 检查服务进程是否运行
```bash
adb shell ps | grep com.seres.background.upgrade
```

### 检查前台服务状态
```bash
adb shell dumpsys activity services | grep BackgroundUpgradeService
adb shell dumpsys activity services | grep UsbDetectionService
```

## 2. 查看日志

### 实时查看应用日志
```bash
adb logcat | grep "BackgroundUpgrade-"
```

### 查看特定组件日志
```bash
# 主服务日志
adb logcat | grep "BackgroundUpgrade-BackgroundUpgradeService"

# USB检测服务日志
adb logcat | grep "BackgroundUpgrade-UsbDetectionService"

# 设备分析器日志
adb logcat | grep "BackgroundUpgrade-DeviceListAnalyzer"

# 任务管理器日志
adb logcat | grep "BackgroundUpgrade-UpgradeTaskManager"

# S2S通信日志
adb logcat | grep "BackgroundUpgrade-S2STaskPublisher"
```

### 保存日志到文件
```bash
adb logcat | grep "BackgroundUpgrade-" > upgrade_service.log
```

## 3. 检查状态文件

### 查看状态文件目录
```bash
adb shell ls -la /Android/data/com.seres.background.upgrade/files/status/
```

### 查看服务状态
```bash
adb shell cat /Android/data/com.seres.background.upgrade/files/status/service_status.json
```

### 查看USB状态
```bash
adb shell cat /Android/data/com.seres.background.upgrade/files/status/usb_status.json
```

### 查看任务状态
```bash
adb shell cat /Android/data/com.seres.background.upgrade/files/status/task_status.json
```

### 查看心跳状态
```bash
adb shell cat /Android/data/com.seres.background.upgrade/files/status/heartbeat.txt
```

## 4. 检查日志文件

### 查看日志文件目录
```bash
adb shell ls -la /Android/data/com.seres.background.upgrade/files/logs/
```

### 查看最新日志文件
```bash
adb shell cat /Android/data/com.seres.background.upgrade/files/logs/background_upgrade_$(date +%Y-%m-%d).log
```

### 下载日志文件到本地
```bash
adb pull /Android/data/com.seres.background.upgrade/files/logs/ ./logs/
```

## 5. 模拟USB插入测试

### 创建测试目录
```bash
adb shell mkdir -p /sdcard/test_usb/
```

### 创建测试device_list.json文件
```bash
adb shell 'cat > /sdcard/test_usb/device_list.json << EOF
{
  "version": "1.0",
  "devices": [
    {
      "deviceId": "HPCM",
      "deviceName": "高性能计算模块",
      "currentVersion": "1.0.0",
      "targetVersion": "1.1.0",
      "seamless": true,
      "packageFileName": "HPCM_v1.1.0.zip"
    }
  ],
  "preconditions": {
    "powerBatteryThreshold": 15,
    "batteryThreshold": 65
  },
  "preferences": {}
}
EOF'
```

### 创建测试升级包文件
```bash
adb shell echo "Test upgrade package content" > /sdcard/test_usb/HPCM_v1.1.0.zip
```

### 手动触发USB检测（需要root权限）
```bash
adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/test_usb
```

## 6. 检查通知

### 查看通知状态
```bash
adb shell dumpsys notification | grep "com.seres.background.upgrade"
```

## 7. 强制启动服务（调试用）

### 启动主服务
```bash
adb shell am start-foreground-service com.seres.background.upgrade/.service.BackgroundUpgradeService
```

### 启动USB检测服务
```bash
adb shell am start-foreground-service com.seres.background.upgrade/.service.UsbDetectionService
```

## 8. 检查权限

### 查看应用权限
```bash
adb shell dumpsys package com.seres.background.upgrade | grep permission
```

### 检查存储权限
```bash
adb shell ls -la /Android/data/com.seres.background.upgrade/
```

## 9. 性能监控

### 查看内存使用
```bash
adb shell dumpsys meminfo com.seres.background.upgrade
```

### 查看CPU使用
```bash
adb shell top | grep com.seres.background.upgrade
```

## 10. 故障排除

### 重启服务
```bash
adb shell am force-stop com.seres.background.upgrade
adb shell am start-foreground-service com.seres.background.upgrade/.service.BackgroundUpgradeService
```

### 清除应用数据（慎用）
```bash
adb shell pm clear com.seres.background.upgrade
```

### 重新安装应用
```bash
adb uninstall com.seres.background.upgrade
adb install BackgroundUpgradeService-debug.apk
```

## 11. 一键检查脚本

### 创建检查脚本
```bash
#!/bin/bash
echo "=== 后台升级服务状态检查 ==="
echo "1. 检查应用是否安装:"
adb shell pm list packages | grep com.seres.background.upgrade

echo -e "\n2. 检查进程是否运行:"
adb shell ps | grep com.seres.background.upgrade

echo -e "\n3. 检查心跳状态:"
adb shell cat /Android/data/com.seres.background.upgrade/files/status/heartbeat.txt 2>/dev/null || echo "心跳文件不存在"

echo -e "\n4. 检查服务状态:"
adb shell cat /Android/data/com.seres.background.upgrade/files/status/service_status.json 2>/dev/null || echo "服务状态文件不存在"

echo -e "\n5. 检查USB状态:"
adb shell cat /Android/data/com.seres.background.upgrade/files/status/usb_status.json 2>/dev/null || echo "USB状态文件不存在"

echo -e "\n6. 最近的日志:"
adb logcat -d | grep "BackgroundUpgrade-" | tail -10
```

保存为 `check_service.sh` 并执行：
```bash
chmod +x check_service.sh
./check_service.sh
```
