# 后台升级服务 (Background Upgrade Service)

## 项目概述

后台升级服务是一个纯后台运行的Android应用程序，专门用于车载系统的OTA升级管理。该服务具有以下特点：

- **无界面设计**：完全后台运行，无用户界面
- **开机自启动**：系统启动后自动运行，无法被用户关闭
- **智能检测**：自动检测包含`device_list.json`的U盘
- **自动处理**：自动分析升级包、拷贝文件、生成任务JSON
- **S2S通信**：通过AIDL与S2S服务通信，转发任务到yocto系统

## 主要功能

### 🔌 USB设备检测
- 实时监听U盘插入/拔出事件
- 自动检测包含`device_list.json`文件的U盘
- 支持多种USB存储设备

### 📦 升级任务处理
- 解析`device_list.json`配置文件
- 扫描并分析升级包文件
- 生成标准格式的升级任务JSON
- 自动拷贝文件到`/ota_share/`目录

### 🌐 S2S通信
- 通过AIDL与S2S服务通信
- 发送`UDiskSrv_UpgradeTaskNotify_OTAM_In`格式的升级任务
- 订阅`InventoryInfoStatus`资产信息
- 进行版本兼容性检查

### 🔍 版本兼容性检查
- 接收来自yocto系统的资产信息
- 对比当前版本与目标版本
- 通过`UDiskSrv_UpgradeTaskIsMatch_OTAM_In`通知升级决策

## 项目结构

```
BackgroundUpgradeService/
├── src/main/java/com/seres/background/upgrade/
│   ├── BackgroundUpgradeApplication.kt      # 应用程序类
│   ├── analyzer/
│   │   └── DeviceListAnalyzer.kt            # 设备列表分析器
│   ├── manager/
│   │   └── UpgradeTaskManager.kt            # 升级任务管理器
│   ├── model/
│   │   └── DeviceInfo.kt                    # 数据模型
│   ├── publisher/
│   │   └── S2STaskPublisher.kt              # S2S任务发布者
│   ├── receiver/
│   │   ├── BootCompleteReceiver.kt          # 开机启动接收器
│   │   └── UsbDeviceReceiver.kt             # USB设备接收器
│   ├── service/
│   │   ├── BackgroundUpgradeService.kt      # 主要后台服务
│   │   └── UsbDetectionService.kt           # USB检测服务
│   └── utils/
│       └── LogUtils.kt                      # 日志工具类
├── src/main/aidl/                           # AIDL接口文件
├── src/main/res/                            # 资源文件
└── build.gradle                             # 构建配置
```

## 配置文件格式

### device_list.json

U盘根目录下必须包含`device_list.json`文件，格式如下：

```json
{
  "version": "1.0",
  "devices": [
    {
      "deviceId": "HPCM",
      "deviceName": "高性能计算模块",
      "currentVersion": "1.0.0",
      "targetVersion": "1.1.0",
      "seamless": true,
      "packageFileName": "HPCM_v1.1.0.zip"
    }
  ],
  "preconditions": {
    "powerBatteryThreshold": 15,
    "batteryThreshold": 65
  },
  "preferences": {
    "autoReboot": true,
    "backupBeforeUpgrade": true
  }
}
```

### 生成的升级任务JSON

系统会自动生成以下格式的升级任务JSON：

```json
{
  "version": "1.0",
  "share_path": "/ota_share/",
  "package_info": [
    {
      "ecu_name": "HPCM",
      "seamless": true,
      "package_path": "/ota_share/HPCM_v1.1.0.zip",
      "package_size": 1024000,
      "package_md5": "d41d8cd98f00b204e9800998ecf8427e"
    }
  ],
  "preconditions": {
    "power_battery_threshold": 15,
    "battery_threshold": 65
  },
  "preferences": {}
}
```

## 安装和配置

### 系统要求
- Android 12+ (API 31+)
- 车载Android系统
- 存储访问权限
- S2S服务支持

### 构建和安装
```bash
# 构建APK
./gradlew :BackgroundUpgradeService:assembleDebug

# 安装到设备
adb install BackgroundUpgradeService/build/outputs/apk/debug/BackgroundUpgradeService-debug.apk
```

### 权限配置
应用需要以下权限：
- `RECEIVE_BOOT_COMPLETED`: 开机自启动
- `FOREGROUND_SERVICE`: 前台服务
- `MANAGE_EXTERNAL_STORAGE`: 存储访问
- `USB_PERMISSION`: USB设备访问

## 使用方法

### 1. 准备升级包
- 创建包含升级文件的U盘
- 在U盘根目录放置`device_list.json`配置文件
- 确保升级包文件名与配置文件中的`packageFileName`匹配

### 2. 插入U盘
- 将U盘插入车载设备
- 服务会自动检测并开始处理升级任务
- 文件会自动拷贝到`/ota_share/`目录

### 3. 监控状态
- 通过日志查看处理状态
- 服务会自动与S2S通信
- 版本兼容性检查会自动进行

## S2S通信协议

### 发送升级任务
- **Hash**: `-320322560` (UDiskSrv_UpgradeTaskNotify_OTAM_HASH)
- **数据**: JSON格式的升级任务信息

### 接收资产信息
- **Hash**: `1001` (InventoryInfoStatus信号)
- **数据**: ECU资产信息列表

### 发送匹配结果
- **Hash**: `1137060134` (UDiskSrv_UpgradeTaskIsMatch_OTAM_HASH)
- **数据**: 布尔值表示是否匹配

## 日志和调试

### 日志文件
- 位置: `/Android/data/com.seres.background.upgrade/files/logs/`
- 格式: `background_upgrade_YYYYMMDD.log`
- 自动清理: 保留7天

### 调试标签
- `BackgroundUpgrade-BackgroundUpgradeService`: 主服务
- `BackgroundUpgrade-UsbDetectionService`: USB检测
- `BackgroundUpgrade-DeviceListAnalyzer`: 设备分析
- `BackgroundUpgrade-UpgradeTaskManager`: 任务管理
- `BackgroundUpgrade-S2STaskPublisher`: S2S通信

## 故障排除

### 常见问题

1. **服务未启动**
   - 检查权限设置
   - 确认开机自启动权限
   - 查看系统日志

2. **USB检测失败**
   - 检查USB格式（推荐FAT32）
   - 确认device_list.json文件存在
   - 验证文件格式正确性

3. **S2S通信失败**
   - 确认S2S服务运行状态
   - 检查包名和Action配置
   - 验证AIDL接口版本

4. **文件拷贝失败**
   - 检查/ota_share/目录权限
   - 确认存储空间充足
   - 验证文件完整性

## 调试和验证

### 验证服务是否运行

#### 方法1: 检查进程
```bash
adb shell ps | grep com.seres.background.upgrade
```
如果服务正在运行，会显示进程信息。

#### 方法2: 检查状态文件
```bash
# 检查心跳文件
adb shell cat /Android/data/com.seres.background.upgrade/files/status/heartbeat.txt

# 检查服务状态
adb shell cat /Android/data/com.seres.background.upgrade/files/status/service_status.json
```

#### 方法3: 查看日志
```bash
adb logcat | grep "BackgroundUpgrade-"
```

#### 方法4: 启动调试界面
```bash
adb shell am start -n com.seres.background.upgrade/.DebugActivity
```

### 验证USB检测功能

#### 方法1: 查看USB状态文件
```bash
adb shell cat /Android/data/com.seres.background.upgrade/files/status/usb_status.json
```

#### 方法2: 创建测试USB目录
```bash
# 创建测试目录
adb shell mkdir -p /sdcard/test_usb/

# 创建device_list.json文件
adb shell 'cat > /sdcard/test_usb/device_list.json << EOF
{
  "version": "1.0",
  "devices": [
    {
      "deviceId": "TEST_ECU",
      "deviceName": "测试ECU",
      "currentVersion": "1.0.0",
      "targetVersion": "1.1.0",
      "seamless": true,
      "packageFileName": "test_package.zip"
    }
  ],
  "preconditions": {
    "powerBatteryThreshold": 15,
    "batteryThreshold": 65
  },
  "preferences": {}
}
EOF'

# 创建测试升级包
adb shell echo "Test package content" > /sdcard/test_usb/test_package.zip

# 模拟USB挂载事件
adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/test_usb
```

#### 方法3: 监控日志
```bash
# 实时监控USB相关日志
adb logcat | grep -E "(UsbDetectionService|DeviceListAnalyzer)"
```

### 状态文件说明

应用会在以下位置创建状态文件：
- **目录**: `/Android/data/com.seres.background.upgrade/files/status/`
- **service_status.json**: 服务运行状态
- **usb_status.json**: USB设备检测状态
- **task_status.json**: 升级任务处理状态
- **heartbeat.txt**: 服务心跳信息

### 日志文件说明

应用会在以下位置创建日志文件：
- **目录**: `/Android/data/com.seres.background.upgrade/files/logs/`
- **文件格式**: `background_upgrade_YYYY-MM-DD.log`
- **保留时间**: 7天

### 常用调试命令

详细的调试命令请参考 `debug_commands.md` 文件。

### 快速验证脚本

创建以下脚本进行快速验证：

```bash
#!/bin/bash
echo "=== 后台升级服务快速验证 ==="

echo "1. 检查应用安装:"
if adb shell pm list packages | grep -q com.seres.background.upgrade; then
    echo "✓ 应用已安装"
else
    echo "✗ 应用未安装"
    exit 1
fi

echo "2. 检查服务进程:"
if adb shell ps | grep -q com.seres.background.upgrade; then
    echo "✓ 服务进程正在运行"
else
    echo "✗ 服务进程未运行"
fi

echo "3. 检查心跳:"
heartbeat=$(adb shell cat /Android/data/com.seres.background.upgrade/files/status/heartbeat.txt 2>/dev/null)
if [ -n "$heartbeat" ]; then
    echo "✓ 心跳正常: $heartbeat"
else
    echo "✗ 心跳文件不存在"
fi

echo "4. 检查最近日志:"
recent_logs=$(adb logcat -d | grep "BackgroundUpgrade-" | tail -3)
if [ -n "$recent_logs" ]; then
    echo "✓ 发现最近日志:"
    echo "$recent_logs"
else
    echo "✗ 未发现最近日志"
fi

echo "验证完成!"
```

## 版本历史

- **v1.0.0**: 初始版本
  - 基本USB检测功能
  - device_list.json解析
  - 升级任务JSON生成
  - S2S通信集成
  - 版本兼容性检查
  - 状态监控和调试功能

## 许可证

本项目为内部开发项目，版权归公司所有。
