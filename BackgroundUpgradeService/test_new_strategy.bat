@echo off
chcp 65001 >nul
echo === Test New Strategy - upgrade_task_info.json ===
echo.

echo 1. Clean up previous test...
adb shell rm -rf /sdcard/test_new_strategy/
adb shell rm -f /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/upgrade_task_*.json
adb shell rm -f /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/latest_upgrade_task.json
echo    Previous test data cleaned

echo 2. Create test directory...
adb shell mkdir -p /sdcard/test_new_strategy/

echo 3. Copy upgrade_task_info.json to test directory...
adb push upgrade_task_info.json /sdcard/test_new_strategy/
echo    upgrade_task_info.json copied

echo 4. Create mock upgrade package files...
adb shell "echo 'Mock upgrade package content for xxx.15.C_250701_8224_00.zip' > /sdcard/test_new_strategy/xxx.15.C_250701_8224_00.zip"
echo    Mock upgrade packages created

echo 5. Verify test files...
echo    Files in test directory:
adb shell ls -la /sdcard/test_new_strategy/
echo.

echo 6. Check upgrade_task_info.json content...
echo    Content preview:
adb shell head -20 /sdcard/test_new_strategy/upgrade_task_info.json
echo.

echo 7. Clear previous logs...
adb logcat -c
echo    Logs cleared

echo 8. Trigger USB mount event...
adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/test_new_strategy
echo    USB mount event triggered

echo 9. Wait 10 seconds for processing...
timeout /t 10 >nul

echo 10. Check logs for new strategy...
echo    Recent logs:
adb logcat -d | findstr "UpgradeTaskAnalyzer\|UpgradeTaskManager.*升级任务信息\|UpgradeTaskManager.*JSON"
echo.

echo 11. Check if task JSON was generated...
echo    Latest upgrade task JSON:
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/latest_upgrade_task.json 2>nul
if %errorlevel% equ 0 (
    echo.
    echo    [SUCCESS] Task JSON generated successfully!
) else (
    echo    [INFO] Task JSON not found
)
echo.

echo 12. Check all task JSON files...
adb shell ls -la /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/upgrade_task_*.json 2>nul
echo.

echo 13. Check USB status...
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/usb_status.json 2>nul
echo.

echo 14. Check task status...
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/task_status.json 2>nul
echo.

echo 15. Check target directories...
echo    /sdcard/ota_share/:
adb shell ls -la /sdcard/ota_share/ 2>nul
echo    /storage/emulated/0/ota_share/:
adb shell ls -la /storage/emulated/0/ota_share/ 2>nul
echo    App files ota_share:
adb shell ls -la /storage/emulated/0/Android/data/com.seres.background.upgrade/files/ota_share/ 2>nul
echo.

echo 16. Show debug activity...
adb shell am start -n com.seres.background.upgrade/.DebugActivity
echo    Debug activity started (click to close)

echo.
echo Test completed!
echo New strategy: Read upgrade_task_info.json directly from USB
echo Expected: JSON should be generated and saved to status folder
pause
